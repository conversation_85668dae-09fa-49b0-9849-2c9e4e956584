#!/usr/bin/env python3

"""
Comprehensive test script to validate the hanging issue fixes.

Tests all aspects mentioned in CRITICAL_FIXES.md Item #9:
- Variable scope issues in exception handling
- SSE headers and termination signals
- Frontend-backend endpoint compatibility
- Vector index corruption handling
- Timeout and circuit breaker functionality
"""

import asyncio
import aiohttp
import json
import time
import sys
from typing import List, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HangingFixValidator:
    def __init__(self, base_url: str = "http://localhost:8082"):
        self.base_url = base_url
        self.session = None
        self.test_results = []
        
    async def __aenter__(self):
        """Async context manager entry."""
        timeout = aiohttp.ClientTimeout(total=30)  # 30 second timeout for tests
        self.session = aiohttp.ClientSession(timeout=timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    def log_test_result(self, test_name: str, passed: bool, details: str = ""):
        """Log test result and add to results."""
        status = "✅ PASS" if passed else "❌ FAIL"
        message = f"{status}: {test_name}"
        if details:
            message += f" - {details}"
        logger.info(message)
        self.test_results.append({
            "test": test_name,
            "passed": passed,
            "details": details
        })
    
    async def test_server_health(self) -> bool:
        """Test if server is running and healthy."""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    self.log_test_result("Server Health Check", True, f"Status: {data.get('status')}")
                    return True
                else:
                    self.log_test_result("Server Health Check", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test_result("Server Health Check", False, f"Connection failed: {e}")
            return False
    
    async def test_post_query_endpoint(self) -> bool:
        """Test the POST /query endpoint for frontend fallback compatibility."""
        try:
            query_data = {"query": "What are DDB's policies on Leaves?"}
            headers = {"Content-Type": "application/json"}
            
            async with self.session.post(
                f"{self.base_url}/query",
                json=query_data,
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    # Check for expected response structure
                    required_fields = ["response", "source_nodes", "follow_up_questions"]
                    if all(field in data for field in required_fields):
                        self.log_test_result("POST /query Endpoint", True, "All required fields present")
                        return True
                    else:
                        missing = [f for f in required_fields if f not in data]
                        self.log_test_result("POST /query Endpoint", False, f"Missing fields: {missing}")
                        return False
                else:
                    self.log_test_result("POST /query Endpoint", False, f"HTTP {response.status}")
                    return False
        except Exception as e:
            self.log_test_result("POST /query Endpoint", False, f"Error: {e}")
            return False
    
    async def test_streaming_endpoint_sse(self) -> bool:
        """Test streaming endpoint for proper SSE format and termination."""
        try:
            query = "What are DDB's guidelines for using AI technologies in internal operations?"
            url = f"{self.base_url}/query/stream?query={query}"
            
            chunks_received = 0
            done_signal_received = False
            proper_sse_format = True
            error_occurred = False
            
            async with self.session.get(url) as response:
                if response.status != 200:
                    self.log_test_result("Streaming SSE Format", False, f"HTTP {response.status}")
                    return False
                
                # Check headers
                content_type = response.headers.get('Content-Type', '')
                if 'text/event-stream' not in content_type:
                    self.log_test_result("Streaming SSE Format", False, f"Wrong content type: {content_type}")
                    return False
                
                # Read stream
                async for line in response.content:
                    line_str = line.decode('utf-8').strip()
                    
                    if line_str.startswith('data: '):
                        chunks_received += 1
                        data_content = line_str[6:]  # Remove 'data: ' prefix
                        
                        if data_content == '[DONE]':
                            done_signal_received = True
                            break
                        
                        try:
                            # Validate JSON format
                            json_data = json.loads(data_content)
                            if 'type' not in json_data:
                                proper_sse_format = False
                                break
                        except json.JSONDecodeError:
                            proper_sse_format = False
                            break
                    
                    # Check for timeout (should not happen with proper implementation)
                    if chunks_received > 1000:  # Reasonable limit
                        error_occurred = True
                        break
            
            # Evaluate results
            if error_occurred:
                self.log_test_result("Streaming SSE Format", False, "Too many chunks or timeout")
                return False
            elif not done_signal_received:
                self.log_test_result("Streaming SSE Format", False, "No [DONE] termination signal")
                return False
            elif not proper_sse_format:
                self.log_test_result("Streaming SSE Format", False, "Invalid SSE format")
                return False
            else:
                self.log_test_result("Streaming SSE Format", True, f"Received {chunks_received} chunks with proper termination")
                return True
                
        except Exception as e:
            self.log_test_result("Streaming SSE Format", False, f"Error: {e}")
            return False
    
    async def test_timeout_handling(self) -> bool:
        """Test timeout handling in streaming endpoint."""
        try:
            # Use a complex query that might timeout in emergency mode but should handle gracefully
            query = "Provide a comprehensive analysis of all DDB policies, procedures, guidelines, and implementation strategies across all departments and regions with detailed examples."
            url = f"{self.base_url}/query/stream?query={query}"
            
            start_time = time.time()
            done_signal_received = False
            error_handled = False
            
            async with self.session.get(url) as response:
                if response.status != 200:
                    self.log_test_result("Timeout Handling", False, f"HTTP {response.status}")
                    return False
                
                async for line in response.content:
                    line_str = line.decode('utf-8').strip()
                    
                    if line_str.startswith('data: '):
                        data_content = line_str[6:]
                        
                        if data_content == '[DONE]':
                            done_signal_received = True
                            break
                        
                        try:
                            json_data = json.loads(data_content)
                            if json_data.get('type') == 'error':
                                error_content = json_data.get('content', '')
                                if 'timeout' in error_content.lower():
                                    error_handled = True
                        except json.JSONDecodeError:
                            pass
            
            elapsed_time = time.time() - start_time
            
            if done_signal_received and elapsed_time < 120:  # Should not take more than 2 minutes
                self.log_test_result("Timeout Handling", True, f"Completed in {elapsed_time:.1f}s")
                return True
            else:
                self.log_test_result("Timeout Handling", False, f"Took too long: {elapsed_time:.1f}s")
                return False
                
        except Exception as e:
            self.log_test_result("Timeout Handling", False, f"Error: {e}")
            return False
    
    async def test_error_recovery(self) -> bool:
        """Test error recovery and fallback mechanisms."""
        try:
            # Test with an empty query to trigger validation error
            query_data = {"query": ""}
            headers = {"Content-Type": "application/json"}
            
            async with self.session.post(
                f"{self.base_url}/query",
                json=query_data,
                headers=headers
            ) as response:
                if response.status == 400:  # Should return validation error
                    data = await response.json()
                    if 'error' in data:
                        self.log_test_result("Error Recovery", True, "Validation error handled correctly")
                        return True
                    else:
                        self.log_test_result("Error Recovery", False, "No error field in response")
                        return False
                else:
                    self.log_test_result("Error Recovery", False, f"Expected 400, got {response.status}")
                    return False
        except Exception as e:
            self.log_test_result("Error Recovery", False, f"Error: {e}")
            return False
    
    async def test_concurrent_requests(self) -> bool:
        """Test concurrent requests to ensure no hanging or resource issues."""
        try:
            tasks = []
            queries = [
                "What are DDB's policies on Leaves?",
                "DDB AI technology guidelines",
                "Company procedures and workflow",
                "Employee handbook information",
                "IT policies and security"
            ]
            
            # Create concurrent streaming requests
            for i, query in enumerate(queries):
                task = asyncio.create_task(self._single_stream_test(query, f"concurrent-{i}"))
                tasks.append(task)
            
            # Wait for all requests to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            successful_requests = sum(1 for r in results if r is True)
            
            if successful_requests >= len(queries) * 0.8:  # 80% success rate
                self.log_test_result("Concurrent Requests", True, f"{successful_requests}/{len(queries)} succeeded")
                return True
            else:
                self.log_test_result("Concurrent Requests", False, f"Only {successful_requests}/{len(queries)} succeeded")
                return False
                
        except Exception as e:
            self.log_test_result("Concurrent Requests", False, f"Error: {e}")
            return False
    
    async def _single_stream_test(self, query: str, test_id: str) -> bool:
        """Helper method for single streaming test."""
        try:
            url = f"{self.base_url}/query/stream?query={query}"
            
            async with self.session.get(url) as response:
                if response.status != 200:
                    return False
                
                done_received = False
                chunk_count = 0
                
                async for line in response.content:
                    line_str = line.decode('utf-8').strip()
                    
                    if line_str.startswith('data: '):
                        chunk_count += 1
                        data_content = line_str[6:]
                        
                        if data_content == '[DONE]':
                            done_received = True
                            break
                        
                        if chunk_count > 500:  # Prevent infinite loops
                            break
                
                return done_received and chunk_count > 0
                
        except Exception:
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and return summary."""
        logger.info("🧪 Starting comprehensive hanging fix validation tests...")
        logger.info("=" * 60)
        
        # Test sequence
        tests = [
            ("Server Health", self.test_server_health),
            ("POST Query Endpoint", self.test_post_query_endpoint),
            ("Streaming SSE Format", self.test_streaming_endpoint_sse),
            ("Timeout Handling", self.test_timeout_handling),
            ("Error Recovery", self.test_error_recovery),
            ("Concurrent Requests", self.test_concurrent_requests),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n🔍 Running: {test_name}")
            try:
                await test_func()
            except Exception as e:
                self.log_test_result(test_name, False, f"Test exception: {e}")
            
            # Brief pause between tests
            await asyncio.sleep(1)
        
        # Generate summary
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['passed'])
        
        logger.info("\n" + "=" * 60)
        logger.info("📊 TEST SUMMARY")
        logger.info("=" * 60)
        
        for result in self.test_results:
            status = "✅" if result['passed'] else "❌"
            logger.info(f"{status} {result['test']}")
            if result['details']:
                logger.info(f"   └─ {result['details']}")
        
        logger.info("\n" + "=" * 60)
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        logger.info(f"📈 Overall Result: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            logger.info("🎉 HANGING ISSUE FIXES VALIDATED SUCCESSFULLY!")
        else:
            logger.info("⚠️  Some tests failed - hanging issues may persist")
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": success_rate,
            "all_passed": passed_tests == total_tests,
            "results": self.test_results
        }

async def main():
    """Main test execution."""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8082"
    
    logger.info(f"Testing server at: {base_url}")
    
    async with HangingFixValidator(base_url) as validator:
        results = await validator.run_all_tests()
        
        # Exit with appropriate code
        exit_code = 0 if results['all_passed'] else 1
        sys.exit(exit_code)

if __name__ == "__main__":
    asyncio.run(main())