#!/usr/bin/env python3
"""
Test script to verify the no documents found functionality
"""
import requests
import json
import time
import sys

BASE_URL = "http://localhost:8082"

def test_authentication():
    """Test basic authentication"""
    print("Testing authentication...")
    
    # Try to log in
    login_data = {
        "username": "admin", 
        "password": "your-password"
    }
    
    session = requests.Session()
    
    # Login
    response = session.post(f"{BASE_URL}/login", data=login_data)
    print(f"Login response status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Login successful!")
        return session
    else:
        print(f"❌ Login failed: {response.text}")
        return None

def test_regular_query_endpoint(session):
    """Test the regular /query endpoint with no documents"""
    print("\n--- Testing Regular Query Endpoint ---")
    
    query = "test query about something that doesn't exist"
    
    try:
        response = session.get(f"{BASE_URL}/query", params={"query": query})
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Regular endpoint returned successfully")
            print(f"Response structure: {list(data.keys())}")
            
            # Check if it correctly detected no documents
            if data.get("no_documents_found"):
                print("✅ Correctly detected no documents found")
                print(f"Message: {data.get('message', 'No message')}")
            else:
                print("❌ Did not detect no documents found")
                print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Regular endpoint failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing regular endpoint: {e}")

def test_streaming_query_endpoint(session):
    """Test the streaming /query/stream endpoint with no documents"""
    print("\n--- Testing Streaming Query Endpoint ---")
    
    query = "test streaming query about something that doesn't exist"
    
    try:
        response = session.get(f"{BASE_URL}/query/stream", params={"query": query}, stream=True)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Streaming endpoint connected successfully")
            
            # Read streaming response
            chunks = []
            completion_data = None
            
            for line in response.iter_lines(decode_unicode=True):
                if line.startswith('data: '):
                    data_content = line[6:]  # Remove 'data: ' prefix
                    
                    if data_content.strip() == '[DONE]':
                        print("✅ Received [DONE] signal")
                        break
                    
                    try:
                        data = json.loads(data_content)
                        if data.get('type') == 'chunk':
                            chunks.append(data.get('content', ''))
                        elif data.get('type') == 'complete':
                            completion_data = data
                            print("✅ Received completion data")
                    except json.JSONDecodeError:
                        # Non-JSON data like ping
                        continue
            
            # Analyze results
            full_response = ''.join(chunks)
            print(f"Full response: {full_response[:200]}...")
            
            if completion_data and completion_data.get('noDocumentsFound'):
                print("✅ Streaming endpoint correctly detected no documents found")
            else:
                print("❌ Streaming endpoint did not detect no documents found")
                print(f"Completion data: {completion_data}")
                
        else:
            print(f"❌ Streaming endpoint failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing streaming endpoint: {e}")

def main():
    print("🧪 Testing No Documents Found Functionality")
    print("=" * 50)
    
    # Test authentication
    session = test_authentication()
    if not session:
        print("❌ Cannot proceed without authentication")
        return False
    
    # Test both endpoints
    test_regular_query_endpoint(session)
    test_streaming_query_endpoint(session)
    
    print("\n" + "=" * 50)
    print("🏁 Testing completed!")

if __name__ == "__main__":
    main()