#!/usr/bin/env python3
"""
Fix Orphaned Documents Script

This script helps fix documents in the index that don't have sharepoint_id metadata
by either:
1. Adding a synthetic sharepoint_id based on filename
2. Removing documents that are truly orphaned (manual files)
3. Re-importing documents from SharePoint if they exist there

Usage:
    python fix_orphaned_documents.py --dry-run    # Show what would be done
    python fix_orphaned_documents.py --fix        # Actually fix the issues
    python fix_orphaned_documents.py --remove     # Remove orphaned documents
"""

import asyncio
import sys
import argparse
from pathlib import Path
import logging
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from config import Settings
from llama_index.core import StorageContext, load_index_from_storage

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def analyze_orphaned_documents():
    """Analyze documents without sharepoint_id to understand the scope of the issue."""
    
    settings = Settings()
    
    # Load the index
    storage_dir = settings.STORAGE_DIR
    try:
        storage_context = StorageContext.from_defaults(persist_dir=str(storage_dir))
        index = load_index_from_storage(storage_context)
        logger.info(f"✅ Loaded index from {storage_dir}")
    except Exception as e:
        logger.error(f"❌ Failed to load index: {e}")
        return
    
    if not hasattr(index, "docstore") or not hasattr(index.docstore, "docs"):
        logger.error("❌ Index doesn't have accessible docstore")
        return
    
    total_docs = len(index.docstore.docs)
    orphaned_docs = []
    sharepoint_docs = []
    
    logger.info(f"🔍 Analyzing {total_docs} documents in the index...")
    
    for doc_id, doc_info in index.docstore.docs.items():
        metadata = doc_info.metadata if doc_info.metadata else {}
        sharepoint_id = metadata.get("sharepoint_id")
        file_name = metadata.get("file_name", "Unknown")
        source = metadata.get("source", "Unknown")
        
        if sharepoint_id:
            sharepoint_docs.append({
                "doc_id": doc_id,
                "sharepoint_id": sharepoint_id,
                "file_name": file_name,
                "source": source
            })
        else:
            orphaned_docs.append({
                "doc_id": doc_id,
                "file_name": file_name,
                "source": source,
                "metadata": metadata
            })
    
    # Print analysis
    print("\n" + "="*60)
    print("ORPHANED DOCUMENTS ANALYSIS")
    print("="*60)
    print(f"📊 Total documents in index: {total_docs}")
    print(f"✅ Documents with sharepoint_id: {len(sharepoint_docs)}")
    print(f"⚠️  Orphaned documents (no sharepoint_id): {len(orphaned_docs)}")
    print(f"📈 Orphaned percentage: {len(orphaned_docs)/total_docs*100:.1f}%")
    
    if orphaned_docs:
        print(f"\n🔍 ORPHANED DOCUMENTS BREAKDOWN:")
        
        # Group by source
        sources = {}
        for doc in orphaned_docs:
            source = doc['source']
            if source not in sources:
                sources[source] = []
            sources[source].append(doc)
        
        for source, docs in sources.items():
            print(f"  📁 Source '{source}': {len(docs)} documents")
            
            # Show first few examples
            for i, doc in enumerate(docs[:3]):
                print(f"    - {doc['file_name']} (doc_id: {doc['doc_id'][:8]}...)")
            
            if len(docs) > 3:
                print(f"    ... and {len(docs)-3} more")
        
        print(f"\n💡 RECOMMENDATIONS:")
        print(f"1. To disable orphaned document notifications:")
        print(f"   Add to .env: ORPHANED_DOCS_EMAIL_ENABLED=false")
        print(f"")
        print(f"2. To fix orphaned documents:")
        print(f"   python fix_orphaned_documents.py --fix")
        print(f"")
        print(f"3. To remove orphaned documents:")
        print(f"   python fix_orphaned_documents.py --remove")
    
    return orphaned_docs, sharepoint_docs

async def fix_orphaned_documents(orphaned_docs, dry_run=True):
    """Fix orphaned documents by adding synthetic sharepoint_id or other metadata."""
    
    if not orphaned_docs:
        logger.info("✅ No orphaned documents to fix")
        return
    
    settings = Settings()
    
    # Load the index
    storage_dir = settings.STORAGE_DIR
    try:
        storage_context = StorageContext.from_defaults(persist_dir=str(storage_dir))
        index = load_index_from_storage(storage_context)
        logger.info(f"✅ Loaded index from {storage_dir}")
    except Exception as e:
        logger.error(f"❌ Failed to load index: {e}")
        return
    
    fixed_count = 0
    
    for doc in orphaned_docs:
        doc_id = doc['doc_id']
        file_name = doc['file_name']
        
        if dry_run:
            logger.info(f"[DRY RUN] Would fix: {file_name} (doc_id: {doc_id[:8]}...)")
        else:
            try:
                # Get the document
                doc_info = index.docstore.docs[doc_id]
                
                # Create a synthetic sharepoint_id based on filename and timestamp
                synthetic_id = f"synthetic_{file_name}_{datetime.now().strftime('%Y%m%d')}"
                
                # Update metadata
                if not doc_info.metadata:
                    doc_info.metadata = {}
                
                doc_info.metadata['sharepoint_id'] = synthetic_id
                doc_info.metadata['source'] = doc_info.metadata.get('source', 'legacy_import')
                doc_info.metadata['fixed_orphan'] = True
                doc_info.metadata['fixed_timestamp'] = datetime.now().isoformat()
                
                # Update the document in the docstore
                index.docstore.docs[doc_id] = doc_info
                
                logger.info(f"✅ Fixed: {file_name} -> sharepoint_id: {synthetic_id}")
                fixed_count += 1
                
            except Exception as e:
                logger.error(f"❌ Failed to fix {file_name}: {e}")
    
    if not dry_run and fixed_count > 0:
        # Persist the changes
        try:
            index.storage_context.persist(persist_dir=str(storage_dir))
            logger.info(f"✅ Persisted {fixed_count} fixes to storage")
        except Exception as e:
            logger.error(f"❌ Failed to persist changes: {e}")
    
    print(f"\n{'[DRY RUN] ' if dry_run else ''}Fixed {fixed_count} orphaned documents")

async def remove_orphaned_documents(orphaned_docs, dry_run=True):
    """Remove orphaned documents from the index."""
    
    if not orphaned_docs:
        logger.info("✅ No orphaned documents to remove")
        return
    
    settings = Settings()
    
    # Load the index
    storage_dir = settings.STORAGE_DIR
    try:
        storage_context = StorageContext.from_defaults(persist_dir=str(storage_dir))
        index = load_index_from_storage(storage_context)
        logger.info(f"✅ Loaded index from {storage_dir}")
    except Exception as e:
        logger.error(f"❌ Failed to load index: {e}")
        return
    
    removed_count = 0
    
    for doc in orphaned_docs:
        doc_id = doc['doc_id']
        file_name = doc['file_name']
        
        if dry_run:
            logger.info(f"[DRY RUN] Would remove: {file_name} (doc_id: {doc_id[:8]}...)")
        else:
            try:
                # Remove from docstore
                if doc_id in index.docstore.docs:
                    del index.docstore.docs[doc_id]
                    logger.info(f"✅ Removed: {file_name}")
                    removed_count += 1
                else:
                    logger.warning(f"⚠️ Document not found: {file_name}")
                    
            except Exception as e:
                logger.error(f"❌ Failed to remove {file_name}: {e}")
    
    if not dry_run and removed_count > 0:
        # Persist the changes
        try:
            index.storage_context.persist(persist_dir=str(storage_dir))
            logger.info(f"✅ Persisted removal of {removed_count} documents to storage")
        except Exception as e:
            logger.error(f"❌ Failed to persist changes: {e}")
    
    print(f"\n{'[DRY RUN] ' if dry_run else ''}Removed {removed_count} orphaned documents")

async def main():
    parser = argparse.ArgumentParser(description="Fix orphaned documents in the search index")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")
    parser.add_argument("--fix", action="store_true", help="Fix orphaned documents by adding synthetic sharepoint_id")
    parser.add_argument("--remove", action="store_true", help="Remove orphaned documents from the index")
    parser.add_argument("--analyze", action="store_true", help="Only analyze and show orphaned documents")
    
    args = parser.parse_args()
    
    if not any([args.dry_run, args.fix, args.remove, args.analyze]):
        print("Please specify an action: --analyze, --dry-run, --fix, or --remove")
        parser.print_help()
        return
    
    # Analyze orphaned documents
    orphaned_docs, sharepoint_docs = await analyze_orphaned_documents()
    
    if args.analyze:
        return
    
    if args.fix:
        await fix_orphaned_documents(orphaned_docs, dry_run=args.dry_run)
    elif args.remove:
        await remove_orphaned_documents(orphaned_docs, dry_run=args.dry_run)

if __name__ == "__main__":
    asyncio.run(main())