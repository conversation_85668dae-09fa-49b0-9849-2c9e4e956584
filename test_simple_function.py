#!/usr/bin/env python3
"""
Simple test of the no documents response logic
"""
import logging

# Mock settings for testing
class MockSettings:
    NO_DOCUMENTS_MESSAGE = "I don't have any relevant documents about this topic in my knowledge base. Please ensure that relevant documents have been uploaded to the system through the SharePoint sync functionality."

settings = MockSettings()

def handle_no_documents_response(query: str, logger):
    """Handle the case when no relevant documents are found for a query."""
    logger.warning(f"No relevant documents found for query: '{query}'")
    
    # Format the query for display in the message
    formatted_query = f'"{query}"' if query else "this topic"
    
    # Get the configured message and customize it with the query
    base_message = settings.NO_DOCUMENTS_MESSAGE
    if "{query}" in base_message:
        no_docs_message = base_message.format(query=formatted_query)
    else:
        no_docs_message = f"I don't have any relevant documents about {formatted_query} in my knowledge base. Please ensure that relevant documents have been uploaded to the system through the SharePoint sync functionality."
    
    return {
        "message": no_docs_message,
        "query": query,
        "sources": [],
        "follow_up_questions": [],
        "source_metadata": {
            "total_sources": 0,
            "relevant_sources": 0,
            "displayed_sources": 0
        },
        "no_documents_found": True
    }

def test_function():
    """Test the handle_no_documents_response function"""
    print("🧪 Testing handle_no_documents_response function")
    print("=" * 50)
    
    # Create a logger for testing
    logger = logging.getLogger("test")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    logger.addHandler(handler)
    
    # Test 1: Normal query
    print("\n--- Test 1: Normal query ---")
    test_query = "What is the company's vacation policy?"
    response = handle_no_documents_response(test_query, logger)
    
    print(f"✅ Response generated")
    print(f"📄 Message: {response['message']}")
    print(f"🔍 Query included: {'vacation policy' in response['message']}")
    print(f"🚫 No docs flag: {response.get('no_documents_found')}")
    
    # Test 2: Empty query
    print("\n--- Test 2: Empty query ---")
    empty_response = handle_no_documents_response("", logger)
    print(f"📄 Message: {empty_response['message']}")
    print(f"🔍 Contains 'this topic': {'this topic' in empty_response['message']}")
    
    # Test 3: Long query
    print("\n--- Test 3: Long query ---")
    long_query = "What are the detailed procedures for requesting time off during the holiday season including approval workflows?"
    long_response = handle_no_documents_response(long_query, logger)
    print(f"📄 Message: {long_response['message']}")
    
    # Verify structure
    print("\n--- Structure Verification ---")
    required_fields = ["message", "query", "sources", "follow_up_questions", "source_metadata", "no_documents_found"]
    for field in required_fields:
        if field in response:
            print(f"✅ {field}: {type(response[field])}")
        else:
            print(f"❌ Missing: {field}")
    
    print("\n" + "=" * 50)
    print("🏁 Function testing completed successfully!")
    
    return True

if __name__ == "__main__":
    test_function()