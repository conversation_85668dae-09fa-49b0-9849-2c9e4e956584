services:
  - type: web
    name: ddb-rag
    env: python
    buildCommand: |
      pip install -r requirements.txt
      # Create the storage directory
      mkdir -p storage
    startCommand: uvicorn app:app --host 0.0.0.0 --port $PORT
    aptPackages:
      - tesseract-ocr
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: OPENAI_API_KEY
        sync: false
      - key: COHERE_API_KEY
        sync: false
      - key: USE_SHAREPOINT
        value: true
      - key: MS_CLIENT_ID
        sync: false
      - key: MS_CLIENT_SECRET
        sync: false
      - key: MS_TENANT_ID
        sync: false
      - key: SHAREPOINT_SITE_NAME
        sync: false
      - key: SHAREPOINT_LIBRARY_NAME
        value: Documents
      - key: USERNAME
        value: admin
      - key: PASSWORD
        sync: false
      - key: SESSION_SECRET_KEY
        sync: false
      - key: FILE_SIZE_LIMIT
        value: "104857600"
      - key: OPENAI_MODEL
        value: gpt-4.1-mini
      - key: CHUNK_SIZE
        value: "512"
      - key: CHUNK_OVERLAP
        value: "50"
      - key: SIMILAR<PERSON>Y_TOP_K
        value: "20"
      - key: CORS_ORIGINS
        value: "http://localhost:8082,http://0.0.0.0:8082,https://*.onrender.com,https://*.railway.app"
    disk:
      name: storage
      mountPath: /opt/render/project/src/storage
      sizeGB: 10 