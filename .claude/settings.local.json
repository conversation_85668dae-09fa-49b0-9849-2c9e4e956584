{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(python:*)", "Bash(rm:*)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "<PERSON><PERSON>(timeout 10s uvicorn:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(uvicorn:*)", "Bash(kill:*)", "Bash(lsof:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(true)", "Bash(pip install:*)", "Bash(ls:*)", "<PERSON><PERSON>(gtimeout:*)", "Bash(unset:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(cp:*)", "Bash(/opt/homebrew/bin/uvicorn app:app --host localhost --port 8082 --reload)", "<PERSON><PERSON>(chmod:*)", "Bash(./test_sync_fix.sh:*)", "<PERSON><PERSON>(jq:*)", "WebFetch(domain:docs.llamaindex.ai)", "WebFetch(domain:github.com)", "WebFetch(domain:community.llamaindex.ai)", "Bash(open http://localhost:8082)", "Bash(/dev/null)", "<PERSON><PERSON>(cat:*)", "Bash(export SMTP_PASSWORD=iarqfsiifucpzbhg)", "<PERSON><PERSON>(env)", "Bash(env:*)", "WebFetch(domain:ddb-sharepoint-rag.onrender.com)", "Bash(git filter-branch:*)", "Bash(git stash:*)", "Bash(FILTER_BRANCH_SQUELCH_WARNING=1 git filter-branch --force --index-filter 'git rm --cached --ignore-unmatch ddb-sharepoint-secret.txt uploads/google-credentials.json' --prune-empty --tag-name-filter cat -- --all)", "Bash(git checkout:*)", "Bash(for branch in main Optimized-for-Render Enhancements)", "Bash(do echo \"=== Checking $branch ===\")", "Bash(done)", "<PERSON><PERSON>(timeout:*)", "Bash(git fetch:*)", "Bash(git merge:*)", "Bash(git push:*)", "Bash(git branch:*)", "<PERSON><PERSON>(pip show:*)", "Bash(deactivate)", "<PERSON><PERSON>(source:*)", "Bash(/Users/<USER>/Desktop/DDBSharepoint/venv/bin/uvicorn --version)", "Bash(./venv/bin/uvicorn app:app --host localhost --port 8082)", "Bash(./venv/bin/uvicorn test_oauth_app:app --host localhost --port 8082)", "Bash(git reset:*)", "Bash(git rm:*)", "<PERSON><PERSON>(git clean:*)"], "deny": []}}