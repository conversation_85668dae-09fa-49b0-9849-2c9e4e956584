# macOS system files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Application specific
storage_backup/
cookies.txt
test.txt
.env
*.log

# Environment variables and secrets
.env.*
!.env.example

# Project specific
data/
storage/
*.log
app.log
uploads/google-credentials.json .DS_Store

# Security - Secret files
ddb-sharepoint-secret.txt
*secret*.txt
*credentials*.json
*key*.json
*.pem
*.key

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
# Usually these files are created by PyInstaller. See the docs for more details.
# https://pyinstaller.readthedocs.io/en/stable/operating-mode.html
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
.pytest_cache/
.hypothesis/
nosetests.xml
coverage.xml
*.cover
*.log

# Environments
.env
.venv
venv/
ENV/
env/
env.bak/
venv.bak/

# IDEs / Editors
.idea/
.vscode/
*.swp
*.swo

# OS generated files
.DS_Store
Thumbs.db

# LlamaIndex / storage
storage/
data/
uploads/
temp/

# Other
.ipynb_checkpoints
fresh_rag_env/
