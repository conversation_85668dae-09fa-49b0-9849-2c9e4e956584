#!/usr/bin/env python3
"""
Clear orphaned notification badges that may be causing false positives.
This script clears deletion approval notifications when auto-delete is enabled.
"""

import requests
import json

def clear_orphaned_deletion_notifications():
    """Clear deletion approval notifications when auto-delete is enabled"""
    
    # First, get the current sync settings to verify auto-delete is enabled
    try:
        settings_response = requests.get("http://localhost:8082/api/sync/settings")
        if settings_response.status_code == 401:
            print("❌ Not authenticated - badge clearing requires admin access")
            return
        
        if settings_response.status_code != 200:
            print(f"❌ Failed to get sync settings: {settings_response.status_code}")
            return
            
        settings = settings_response.json()
        auto_delete_enabled = settings.get("AUTO_DELETE_ENABLED", False)
        manual_approval_required = settings.get("AUTO_SYNC_REQUIRE_MANUAL_APPROVAL", False)
        
        print(f"📋 Current Settings:")
        print(f"   AUTO_DELETE_ENABLED: {auto_delete_enabled}")
        print(f"   AUTO_SYNC_REQUIRE_MANUAL_APPROVAL: {manual_approval_required}")
        
        if not auto_delete_enabled:
            print("✅ Auto-delete is disabled, badges are legitimate")
            return
            
        if manual_approval_required:
            print("✅ Manual approval is required, badges are legitimate")
            return
            
        # Get current notifications
        notifications_response = requests.get("http://localhost:8082/api/sync/notifications")
        if notifications_response.status_code != 200:
            print(f"❌ Failed to get notifications: {notifications_response.status_code}")
            return
            
        notifications = notifications_response.json()
        deletion_notifications = [n for n in notifications.get("notifications", []) 
                                if n.get("type") == "deletion_approval_needed" 
                                and not n.get("acknowledged", False)]
        
        print(f"\n🔍 Found {len(deletion_notifications)} unacknowledged deletion approval notifications")
        
        if not deletion_notifications:
            print("✅ No orphaned deletion notifications found")
            return
            
        # Clear each deletion approval notification
        cleared_count = 0
        for notification in deletion_notifications:
            notification_id = notification.get("id")
            if notification_id:
                # Acknowledge the notification
                ack_response = requests.post(f"http://localhost:8082/api/sync/notifications/{notification_id}/acknowledge")
                if ack_response.status_code == 200:
                    cleared_count += 1
                    print(f"✅ Cleared notification: {notification.get('message', 'Unknown')}")
                else:
                    print(f"❌ Failed to clear notification {notification_id}: {ack_response.status_code}")
                    
        print(f"\n🎯 Summary: Cleared {cleared_count} orphaned deletion approval notifications")
        print("📱 Badge count should now be updated in the UI")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🧹 Clearing orphaned notification badges...")
    clear_orphaned_deletion_notifications()