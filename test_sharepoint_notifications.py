#!/usr/bin/env python3
"""
Comprehensive SharePoint File Change Notification Test

This script tests the complete notification system for SharePoint file changes:
1. Drive discovery and configuration
2. Webhook subscription setup (with HTTPS awareness)
3. Background sync functionality
4. Email and sidebar notification delivery
5. End-to-end file change detection

Usage:
    python test_sharepoint_notifications.py
"""

import asyncio
import sys
import json
import requests
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

# Add current directory to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from sharepoint_client import SharePointClient
from config import settings

class SharePointNotificationTester:
    """Comprehensive tester for SharePoint notification system."""
    
    def __init__(self):
        self.client = SharePointClient()
        self.base_url = f"http://localhost:{settings.PORT}"
        self.test_results = {}
        
    async def run_comprehensive_test(self):
        """Run all notification system tests."""
        
        print("🧪 SharePoint Notification System Comprehensive Test")
        print("=" * 60)
        print(f"Target Folder: {self.client.target_folder}")
        print(f"Environment: {settings.ENVIRONMENT}")
        print(f"Webhook Enabled: {settings.ENABLE_SHAREPOINT_WEBHOOKS}")
        print()
        
        # Test 1: Drive Discovery
        print("📋 Test 1: Drive Discovery and Configuration")
        print("-" * 40)
        await self.test_drive_discovery()
        print()
        
        # Test 2: Authentication and Token
        print("🔐 Test 2: Authentication and Token Access")
        print("-" * 40)
        await self.test_token_access()
        print()
        
        # Test 3: Webhook Configuration
        print("🔗 Test 3: Webhook Configuration and HTTPS Requirements")
        print("-" * 40)
        await self.test_webhook_configuration()
        print()
        
        # Test 4: Background Sync System
        print("🔄 Test 4: Background Sync System")
        print("-" * 40)
        await self.test_background_sync()
        print()
        
        # Test 5: Notification Delivery
        print("📧 Test 5: Notification Delivery (Email + Sidebar)")
        print("-" * 40)
        await self.test_notification_delivery()
        print()
        
        # Test 6: File Change Detection Simulation
        print("📁 Test 6: File Change Detection Simulation")
        print("-" * 40)
        await self.test_file_change_detection()
        print()
        
        # Generate Final Report
        print("📊 Final Test Report")
        print("=" * 40)
        self.generate_final_report()
        
    async def test_drive_discovery(self):
        """Test drive discovery functionality."""
        try:
            # Get token first
            token = await self.client.get_cached_token_with_refresh(
                cache_dir=Path("storage/data/token_caches")
            )
            
            if not token:
                self.test_results['drive_discovery'] = {
                    'status': 'FAILED',
                    'error': 'No access token available',
                    'recommendation': 'Ensure user is logged in to SharePoint via the web interface'
                }
                print("❌ Drive discovery failed: No access token")
                return
            
            print("✅ Access token acquired for drive discovery")
            
            # Test drive discovery
            drive_id = await self.client.get_effective_drive_id(token)
            
            if drive_id:
                print(f"✅ Effective drive ID found: {drive_id}")
                
                # Test target folder access
                files = await self.client.list_files_from_drive(drive_id, self.client.target_folder, token)
                
                if files is not None:
                    print(f"✅ Target folder '{self.client.target_folder}' accessible with {len(files)} files")
                    self.test_results['drive_discovery'] = {
                        'status': 'PASSED',
                        'drive_id': drive_id,
                        'folder_accessible': True,
                        'file_count': len(files)
                    }
                else:
                    print(f"❌ Target folder '{self.client.target_folder}' not accessible")
                    self.test_results['drive_discovery'] = {
                        'status': 'FAILED',
                        'drive_id': drive_id,
                        'folder_accessible': False,
                        'error': 'Target folder not accessible'
                    }
            else:
                print("❌ No valid drive ID found")
                self.test_results['drive_discovery'] = {
                    'status': 'FAILED',
                    'error': 'No valid drive ID found',
                    'recommendation': 'Check SharePoint permissions and folder name'
                }
                
        except Exception as e:
            print(f"❌ Drive discovery test failed: {str(e)}")
            self.test_results['drive_discovery'] = {
                'status': 'ERROR',
                'error': str(e)
            }
    
    async def test_token_access(self):
        """Test token access and validation."""
        try:
            token = await self.client.get_cached_token_with_refresh(
                cache_dir=Path("storage/data/token_caches")
            )
            
            if token:
                # Test token validation
                is_valid = await self.client.validate_token(token)
                
                if is_valid:
                    print("✅ Token is valid and functional")
                    self.test_results['token_access'] = {
                        'status': 'PASSED',
                        'token_valid': True
                    }
                else:
                    print("❌ Token validation failed")
                    self.test_results['token_access'] = {
                        'status': 'FAILED',
                        'token_valid': False,
                        'error': 'Token validation failed'
                    }
            else:
                print("❌ No token available")
                self.test_results['token_access'] = {
                    'status': 'FAILED',
                    'error': 'No token available'
                }
                
        except Exception as e:
            print(f"❌ Token access test failed: {str(e)}")
            self.test_results['token_access'] = {
                'status': 'ERROR',
                'error': str(e)
            }
    
    async def test_webhook_configuration(self):
        """Test webhook configuration and HTTPS requirements."""
        try:
            webhook_url = settings.effective_webhook_url
            webhook_supported = settings.webhook_supported_in_environment
            
            print(f"Webhook URL: {webhook_url}")
            print(f"Webhook supported: {webhook_supported}")
            print(f"Environment: {settings.ENVIRONMENT}")
            
            if settings.ENVIRONMENT == "development":
                print("⚠️  Development environment: Microsoft Graph will reject HTTP webhooks")
                print("   📝 For production, configure HTTPS webhook URL")
                print("   💡 Recommendation: Use ngrok or deploy to production for full webhook testing")
                
                self.test_results['webhook_configuration'] = {
                    'status': 'WARNING',
                    'webhook_url': webhook_url,
                    'supported': webhook_supported,
                    'note': 'HTTP webhooks not supported by Microsoft Graph',
                    'recommendation': 'Use HTTPS URL or deploy to production'
                }
            else:
                if webhook_url and webhook_url.startswith("https://"):
                    print("✅ HTTPS webhook URL configured for production")
                    self.test_results['webhook_configuration'] = {
                        'status': 'PASSED',
                        'webhook_url': webhook_url,
                        'supported': webhook_supported
                    }
                else:
                    print("❌ Production environment requires HTTPS webhook URL")
                    self.test_results['webhook_configuration'] = {
                        'status': 'FAILED',
                        'error': 'HTTPS webhook URL required for production',
                        'recommendation': 'Configure WEBHOOK_NOTIFICATION_URL with HTTPS URL'
                    }
                    
        except Exception as e:
            print(f"❌ Webhook configuration test failed: {str(e)}")
            self.test_results['webhook_configuration'] = {
                'status': 'ERROR',
                'error': str(e)
            }
    
    async def test_background_sync(self):
        """Test background sync functionality."""
        try:
            # Test if background sync is configured
            if settings.AUTO_SYNC_ENABLED:
                print("✅ Background sync is enabled")
                print(f"   📅 Sync interval: {settings.SYNC_INTERVAL_MINUTES} minutes")
                print(f"   📂 Target folder: {settings.SYNC_TARGET_FOLDER_PATH}")
                
                # Test if we can reach the sync endpoint
                response = requests.get(f"{self.base_url}/api/sync/background-status")
                
                if response.status_code == 200:
                    sync_status = response.json()
                    print("✅ Background sync status endpoint accessible")
                    print(f"   ⏰ Last sync: {sync_status.get('last_sync_time', 'Never')}")
                    print(f"   📊 Status: {sync_status.get('status', 'Unknown')}")
                    
                    self.test_results['background_sync'] = {
                        'status': 'PASSED',
                        'enabled': True,
                        'sync_status': sync_status
                    }
                else:
                    print(f"❌ Background sync status endpoint not accessible: {response.status_code}")
                    self.test_results['background_sync'] = {
                        'status': 'FAILED',
                        'error': f"Status endpoint returned {response.status_code}"
                    }
            else:
                print("⚠️  Background sync is disabled")
                self.test_results['background_sync'] = {
                    'status': 'WARNING',
                    'enabled': False,
                    'note': 'Background sync is disabled'
                }
                
        except Exception as e:
            print(f"❌ Background sync test failed: {str(e)}")
            self.test_results['background_sync'] = {
                'status': 'ERROR',
                'error': str(e)
            }
    
    async def test_notification_delivery(self):
        """Test notification delivery system."""
        try:
            # Test email configuration
            if settings.EMAIL_ENABLED:
                print("✅ Email notifications enabled")
                print(f"   📧 SMTP Server: {settings.SMTP_SERVER}:{settings.SMTP_PORT}")
                print(f"   👤 From: {settings.SMTP_EMAIL}")
                
                # Test email delivery (would require proper authentication)
                email_status = "CONFIGURED"
            else:
                print("❌ Email notifications disabled")
                email_status = "DISABLED"
            
            # Test notification endpoints
            try:
                response = requests.get(f"{self.base_url}/api/sync/notifications")
                if response.status_code == 200:
                    notifications = response.json()
                    print(f"✅ Notification endpoint accessible ({len(notifications)} notifications)")
                    sidebar_status = "ACCESSIBLE"
                else:
                    print(f"❌ Notification endpoint not accessible: {response.status_code}")
                    sidebar_status = "FAILED"
            except:
                sidebar_status = "ERROR"
            
            self.test_results['notification_delivery'] = {
                'status': 'PARTIAL' if email_status == "CONFIGURED" and sidebar_status == "ACCESSIBLE" else 'WARNING',
                'email_status': email_status,
                'sidebar_status': sidebar_status,
                'email_enabled': settings.EMAIL_ENABLED
            }
            
        except Exception as e:
            print(f"❌ Notification delivery test failed: {str(e)}")
            self.test_results['notification_delivery'] = {
                'status': 'ERROR',
                'error': str(e)
            }
    
    async def test_file_change_detection(self):
        """Test file change detection simulation."""
        try:
            # Get current drive ID and token
            token = await self.client.get_cached_token_with_refresh(
                cache_dir=Path("storage/data/token_caches")
            )
            
            if not token:
                print("❌ Cannot test file change detection: No token")
                self.test_results['file_change_detection'] = {
                    'status': 'FAILED',
                    'error': 'No access token'
                }
                return
            
            drive_id = await self.client.get_effective_drive_id(token)
            
            if not drive_id:
                print("❌ Cannot test file change detection: No drive ID")
                self.test_results['file_change_detection'] = {
                    'status': 'FAILED',
                    'error': 'No valid drive ID'
                }
                return
            
            # Test current file listing
            files = await self.client.list_files_from_drive(drive_id, self.client.target_folder, token)
            
            if files is not None:
                print(f"✅ Current file listing successful ({len(files)} files)")
                print("   📝 File change detection simulation:")
                print("   1. Manual sync endpoint would detect changes")
                print("   2. Background sync would run every few minutes")
                print("   3. Notifications would be sent for any changes")
                
                # Simulate testing the manual sync endpoint
                print("   🧪 Testing manual sync endpoint...")
                try:
                    # This would require authentication in the actual request
                    print("   ⚠️  Manual sync test requires authenticated session")
                    print("   💡 Recommendation: Test via web interface with admin login")
                    
                    self.test_results['file_change_detection'] = {
                        'status': 'SIMULATED',
                        'current_files': len(files),
                        'detection_method': 'background_sync + manual_sync',
                        'note': 'File change detection is configured and ready'
                    }
                except Exception as e:
                    print(f"   ❌ Manual sync test failed: {str(e)}")
                    self.test_results['file_change_detection'] = {
                        'status': 'PARTIAL',
                        'current_files': len(files),
                        'error': str(e)
                    }
            else:
                print("❌ File listing failed")
                self.test_results['file_change_detection'] = {
                    'status': 'FAILED',
                    'error': 'File listing failed'
                }
                
        except Exception as e:
            print(f"❌ File change detection test failed: {str(e)}")
            self.test_results['file_change_detection'] = {
                'status': 'ERROR',
                'error': str(e)
            }
    
    def generate_final_report(self):
        """Generate a comprehensive final report."""
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results.values() if r['status'] == 'PASSED'])
        failed_tests = len([r for r in self.test_results.values() if r['status'] == 'FAILED'])
        warning_tests = len([r for r in self.test_results.values() if r['status'] in ['WARNING', 'PARTIAL', 'SIMULATED']])
        error_tests = len([r for r in self.test_results.values() if r['status'] == 'ERROR'])
        
        print(f"📊 Test Summary: {passed_tests} passed, {failed_tests} failed, {warning_tests} warnings, {error_tests} errors")
        print()
        
        for test_name, result in self.test_results.items():
            status_emoji = {
                'PASSED': '✅',
                'FAILED': '❌', 
                'WARNING': '⚠️',
                'PARTIAL': '🔶',
                'SIMULATED': '🧪',
                'ERROR': '💥'
            }.get(result['status'], '❓')
            
            print(f"{status_emoji} {test_name.replace('_', ' ').title()}: {result['status']}")
            
            if 'error' in result:
                print(f"   ❌ Error: {result['error']}")
            if 'recommendation' in result:
                print(f"   💡 Recommendation: {result['recommendation']}")
            if 'note' in result:
                print(f"   📝 Note: {result['note']}")
        
        print()
        print("🎯 Next Steps for Full SharePoint Notification Setup:")
        print()
        
        # Dynamic recommendations based on test results
        if self.test_results.get('drive_discovery', {}).get('status') == 'FAILED':
            print("1. 🔧 Fix drive discovery:")
            print("   - Verify SharePoint permissions for the service account")
            print("   - Check SYNC_TARGET_FOLDER_PATH environment variable")
            print("   - Ensure target folder exists in SharePoint")
            print()
        
        if self.test_results.get('webhook_configuration', {}).get('status') in ['WARNING', 'FAILED']:
            print("2. 🌐 Set up HTTPS webhooks for production:")
            print("   - Deploy application to production with HTTPS")
            print("   - Configure WEBHOOK_NOTIFICATION_URL with HTTPS URL")
            print("   - Or use ngrok for local HTTPS testing: ngrok http 8082")
            print()
        
        if self.test_results.get('notification_delivery', {}).get('email_status') == 'DISABLED':
            print("3. 📧 Enable email notifications:")
            print("   - Set EMAIL_ENABLED=true in .env")
            print("   - Configure SMTP settings with valid credentials")
            print()
        
        print("4. 🧪 Test the complete flow:")
        print("   - Upload a file to the SharePoint target folder")
        print("   - Wait for background sync or trigger manual sync")
        print("   - Check email and sidebar notifications")
        print()
        
        # Save detailed report
        report_file = Path("sharepoint_notification_test_report.json")
        with open(report_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'environment': settings.ENVIRONMENT,
                'test_results': self.test_results,
                'summary': {
                    'total_tests': total_tests,
                    'passed': passed_tests,
                    'failed': failed_tests,
                    'warnings': warning_tests,
                    'errors': error_tests
                }
            }, f, indent=2)
        
        print(f"📄 Detailed report saved to: {report_file}")

async def main():
    """Main function to run comprehensive notification tests."""
    tester = SharePointNotificationTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())