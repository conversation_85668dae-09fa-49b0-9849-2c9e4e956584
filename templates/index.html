<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DDBrain v2.6</title>
    <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="expires" content="0">
    <script src="/config.js?v=2.6"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.0.2/marked.min.js"></script>
    <style>
        /* Root variables */
        :root {
            --ddb-yellow: #FFD100;
            --ddb-black: #000000;
            --ddb-gray: #2D2D2D;
            --ddb-light-gray: #F5F5F5;
            --bg-primary: #ffffff;
            --bg-secondary: #f5f5f5;
            --text-primary: #000000;
            --text-secondary: #4b5563;
            --border-color: #e5e7eb;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* Dark mode variables */
        [data-theme="dark"] {
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --text-primary: #ffffff;
            --text-secondary: #e5e7eb;
            --border-color: #404040;
            --shadow-color: rgba(0, 0, 0, 0.3);
        }

        /* Base styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        /* Login screen */
        #loginScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--ddb-black);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 50;
        }

        .login-container {
            background-color: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .login-logo {
            width: 120px;
            height: auto;
            margin: 0 auto 1.5rem;
            display: block;
            object-fit: contain;
        }

        .login-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--ddb-yellow);
        }

        .login-input {
            width: 100%;
            padding: 0.75rem;
            margin-bottom: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            transition: all 0.2s;
        }

        .login-input:focus {
            border-color: var(--ddb-yellow);
            outline: none;
            box-shadow: 0 0 0 2px rgba(255, 209, 0, 0.2);
        }

        .login-btn {
            width: 100%;
            padding: 0.75rem;
            background-color: var(--ddb-yellow);
            color: var(--ddb-black);
            font-weight: 500;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            transition: all 0.2s;
        }

        .login-btn:hover {
            background-color: #e6bc00;
        }

        .guest-btn {
            width: 100%;
            padding: 0.75rem;
            background-color: #e5e7eb;
            color: #374151;
            font-weight: 500;
            border-radius: 0.375rem;
            transition: all 0.2s;
        }

        .guest-btn:hover {
            background-color: #d1d5db;
        }

        /* Hide main content initially */
        #mainContent {
            display: none;
            padding-top: 4rem; /* Add padding-top to offset fixed header */
        }

        /* Header styles */
        .fixed-header {
            position: fixed; /* Changed from sticky to fixed */
            top: 0;
            left: 0; /* Added left: 0 */
            right: 0; /* Added right: 0 */
            background-color: var(--ddb-black);
            z-index: 40;
            border-bottom: 1px solid var(--ddb-yellow);
            padding: 0.75rem 1rem;
        }

        /* Burger menu */
        .burger-menu {
            display: none;
            cursor: pointer;
            padding: 0.75rem;
            margin-right: 1rem;
        }

        .burger-menu div {
            width: 28px;
            height: 3px;
            background-color: white;
            margin: 7px 0;
            transition: all 0.3s ease;
        }

        /* Sidebar toggle button */
        .sidebar-toggle {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-right: 1rem;
        }

        .sidebar-toggle svg {
            width: 28px;
            height: 28px;
            fill: currentColor;
        }

        /* Logo and title */
        .logo-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-container img {
            height: 2.5rem;
            width: auto;
        }

        /* Main content area */
        .content-wrapper {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1rem;
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 2rem;
            height: calc(100vh - 4rem);
            transition: grid-template-columns 0.3s ease;
        }

        .content-wrapper.sidebar-hidden {
            grid-template-columns: 0 1fr;
        }

        /* Sidebar styles */
        #sidebar {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            height: 100%;
            overflow-y: auto;
            min-width: 400px;
            transition: transform 0.3s ease, opacity 0.3s ease;
        }

        .sidebar-hidden #sidebar {
            transform: translateX(-100%);
            opacity: 0;
            pointer-events: none;
        }

        /* Document list styles */
        #documentList .document-item {
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            margin-bottom: 0.75rem;
            background-color: white;
            transition: all 0.2s;
            word-wrap: break-word;
            overflow-wrap: break-word;
            word-break: break-word;
        }

        #documentList .document-item:hover {
            border-color: var(--ddb-yellow);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .document-checkbox {
            margin-right: 0.75rem;
            width: 1.2rem;
            height: 1.2rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.25rem;
            cursor: pointer;
        }

        .document-checkbox:checked {
            background-color: var(--ddb-yellow);
            border-color: var(--ddb-yellow);
        }

        .select-all-container {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.5rem;
            background-color: #f9fafb;
            border-radius: 0.375rem;
        }

        .delete-selected-btn {
            display: none;
            background-color: #ef4444;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s;
            margin-left: auto;
        }

        .delete-selected-btn:hover {
            background-color: #dc2626;
        }

        #documentList .document-item h3 {
            color: var(--ddb-black);
            word-wrap: break-word;
            overflow-wrap: break-word;
            word-break: break-word;
            max-width: 100%;
        }

        #documentList .document-item p {
            color: #6b7280;
            word-wrap: break-word;
            overflow-wrap: break-word;
            word-break: break-word;
            max-width: 100%;
        }

        /* Chat container */
        #chatContainer {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            height: 100%; /* Ensure it takes full height of its grid cell */
            display: flex;
            flex-direction: column;
            overflow: hidden; /* Prevent inner content from overflowing */
        }

        /* Chat messages */
        #chatMessages {
            flex: 1 1 auto; /* Allow chat messages to grow and shrink */
            overflow-y: auto; /* Enable scrolling for messages */
            margin-bottom: 1rem; /* Keep some space before the input */
        }

        /* Input container */
        .chat-input-container { /* New container for input + send */
            flex-shrink: 0; /* Prevent input area from shrinking */
            display: flex;
            gap: 0.5rem; /* Use gap instead of margin */
            align-items: center; /* Align items vertically */
        }

        /* Input field adjustments */
        #queryInput {
            flex-grow: 1; /* Allow input to take available space */
            margin-bottom: 0; /* Remove bottom margin as gap handles spacing */
        }

        /* Send button adjustments */
        #sendButton {
            flex-shrink: 0; /* Prevent button from shrinking */
        }

        /* Buttons */
        .btn-primary {
            background-color: var(--ddb-yellow);
            color: var(--ddb-black);
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background-color: #e6bc00;
        }

        .btn-secondary {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s;
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background-color: var(--bg-primary);
        }

        /* Input styles */
        .input-field {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            transition: all 0.2s;
            background-color: var(--bg-primary);
        }

        .input-field:focus {
            border-color: var(--ddb-yellow);
            outline: none;
            box-shadow: 0 0 0 2px rgba(255, 209, 0, 0.2);
        }

        .input-field::placeholder {
            color: var(--text-secondary);
        }

        /* Source citations */
        .source-citation {
            background-color: #f8fafc;
            border-left: 3px solid var(--ddb-yellow);
            padding: 1rem;
            margin: 1rem 0;
            font-size: 0.875rem;
        }

        .source-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            font-weight: 500;
        }

        .source-header:hover {
            color: var(--ddb-black);
        }

        .source-content {
            margin-top: 0.5rem;
            padding-left: 1.5rem;
            display: none;
        }

        .source-arrow {
            transition: transform 0.2s;
            display: inline-block;
            width: 20px;
            height: 20px;
            text-align: center;
            line-height: 20px;
        }

        .source-arrow.expanded {
            transform: rotate(90deg);
        }

        /* Source citations and recommended prompts sections */
        .sources-section, .prompts-section {
            display: block;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e5e7eb;
        }

        .sources-content, .prompts-content {
            display: block;
            margin-top: 1rem;
        }

        .sources-header, .prompts-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            font-weight: 600;
            color: var(--ddb-black);
            margin-bottom: 1rem;
        }

        .sources-header:hover, .prompts-header:hover {
            opacity: 0.8;
        }

        .prompts-content {
            display: none;
        }

        /* Recommended prompts */
        .recommended-prompt {
            display: inline-block;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            background-color: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 1rem;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s;
            user-select: none;
            font-weight: 500;
        }

        .recommended-prompt:hover {
            background-color: var(--ddb-yellow);
            color: var(--ddb-black);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                height: calc(100vh - 4rem);
                padding: 0;
                overflow: hidden;
            }

            /* Welcome screen mobile styles */
            .welcome-container {
                min-height: 50vh;
                padding: 1rem;
            }

            .welcome-title {
                font-size: 2rem;
            }

            .welcome-subtitle {
                font-size: 1rem;
            }

            .questions-header {
                font-size: 1.25rem;
                margin-bottom: 1.5rem;
            }

            .questions-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .question-card {
                padding: 1.25rem;
            }

            .question-icon {
                font-size: 1.75rem;
                margin-bottom: 0.75rem;
            }

            .question-category {
                font-size: 1rem;
            }

            .question-text {
                font-size: 0.9rem;
            }

            #chatContainer {
                border-radius: 0;
                height: 100%;
                position: relative;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            #chatMessages {
                flex: 1;
                overflow-y: auto;
                padding: 1rem;
                padding-bottom: 80px;
            }

            .chat-input-container {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                flex-shrink: 0;
                border-top: 1px solid var(--border-color);
                background-color: var(--bg-primary);
                padding: 1rem;
            }

            #sidebar {
                position: fixed;
                left: 0;
                top: 4rem;
                bottom: 0;
                z-index: 30;
                transform: translateX(-100%);
                opacity: 0;
            }

            #sidebar.mobile-visible {
                transform: translateX(0);
                opacity: 1;
            }

            .burger-menu {
                display: block;
            }

            .sidebar-toggle {
                display: none;
            }
        }

        /* Sidebar text colors */
        #sidebar h2, #sidebar h3 {
            color: var(--text-primary);
        }

        #sidebar p {
            color: var(--text-secondary);
        }

        #documentList .document-item h3 {
            color: var(--text-primary);
        }

        #documentList .document-item p {
            color: var(--text-secondary);
        }

        /* Chat functionality */
        #chatMessages .user-message {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        #chatMessages .assistant-message {
            background-color: var(--bg-primary);
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        /* Dark mode specific styles */
        [data-theme="dark"] .login-container {
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }

        [data-theme="dark"] .login-input {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .login-title {
            color: var(--text-primary);
        }

        [data-theme="dark"] #sidebar {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] #documentList .document-item {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] #documentList .document-item:hover {
            border-color: var(--ddb-yellow);
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] #documentList .document-item h3 {
            color: var(--text-primary);
        }

        [data-theme="dark"] #documentList .document-item p {
            color: var(--text-secondary);
        }

        [data-theme="dark"] #chatContainer {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .chat-message {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        [data-theme="dark"] .user-message {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .assistant-message {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .input-field {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .source-citation {
            background-color: #374151;
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .source-citation .font-semibold,
        [data-theme="dark"] .source-citation .font-semibold.text-gray-900 {
            color: #ffffff !important;
        }

        [data-theme="dark"] .source-citation .text-sm.text-gray-700,
        [data-theme="dark"] .source-citation div.text-sm.text-gray-700 {
            color: #e5e7eb !important;
        }

        [data-theme="dark"] .source-citation .text-xs.text-gray-500,
        [data-theme="dark"] .source-citation div.text-xs.text-gray-500,
        [data-theme="dark"] .source-citation span {
            color: #d1d5db !important;
        }

        [data-theme="dark"] .source-citation:hover {
            background-color: #4b5563;
            border-color: var(--ddb-yellow);
        }

        [data-theme="dark"] .source-header {
            color: var(--text-primary);
        }

        [data-theme="dark"] .source-content {
            color: var(--text-secondary);
        }

        [data-theme="dark"] .recommended-prompt {
            background-color: var(--bg-secondary);
            border-color: #374151;
            color: var(--text-primary);
        }

        [data-theme="dark"] .recommended-prompt:hover {
            background-color: var(--ddb-yellow);
            border-color: var(--ddb-yellow);
            color: var(--ddb-black);
        }

        [data-theme="dark"] .sources-header,
        [data-theme="dark"] .prompts-header {
            color: var(--text-primary);
        }

        [data-theme="dark"] .btn-secondary {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .btn-secondary:hover {
            background-color: var(--bg-primary);
        }

        /* New chat button specific styles */
        #newChatBtn {
            background-color: transparent;
            padding: 0.5rem;
            position: relative;
        }

        #newChatBtn:hover {
            background-color: transparent;
        }

        /* Dark mode toggle button specific styles */
        #darkModeToggle {
            background-color: transparent;
            padding: 0.5rem;
            border: none;
            position: relative;
        }

        #darkModeToggle:hover {
            background-color: transparent;
        }

        #darkModeToggle svg {
            stroke: white;
            transition: stroke 0.2s;
        }

        #darkModeToggle:hover svg {
            stroke: var(--ddb-yellow);
        }

        /* Logout button specific styles */
        #logoutBtn {
            background-color: transparent;
            padding: 0.5rem;
            border: none;
            position: relative;
        }

        #logoutBtn:hover {
            background-color: transparent;
        }

        /* Admin tab button styles */
        .admin-tab-btn {
            background-color: transparent;
            padding: 0.5rem;
            border: none;
            position: relative;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
        }

        .admin-tab-btn svg {
            stroke: white;
            transition: stroke 0.2s;
        }

        .admin-tab-btn:hover {
            background-color: rgba(255, 209, 0, 0.1);
        }

        .admin-tab-btn:hover svg {
            stroke: var(--ddb-yellow);
        }

        .admin-tab-btn:hover .admin-tab-text {
            color: var(--ddb-yellow);
        }

        .admin-tab-btn.active {
            background-color: var(--ddb-yellow);
        }

        .admin-tab-btn.active svg {
            stroke: var(--ddb-black);
        }

        .admin-tab-btn.active .admin-tab-text {
            color: var(--ddb-black);
        }

        .admin-tab-text {
            color: white;
            font-size: 0.875rem;
            font-weight: 500;
            transition: color 0.2s;
        }

        /* Admin tab tooltip */
        .admin-tab-btn::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: -45px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 50;
        }

        .admin-tab-btn::before {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            border-width: 6px;
            border-style: solid;
            border-color: transparent transparent rgba(0, 0, 0, 0.9) transparent;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 51;
        }

        .admin-tab-btn:hover::after,
        .admin-tab-btn:hover::before {
            opacity: 1;
            visibility: visible;
        }

        /* Admin Dashboard Styles */
        .admin-dashboard {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--bg-primary);
            z-index: 40;
            overflow-y: auto;
            padding-top: 80px; /* Account for fixed header */
        }

        .admin-dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .admin-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .admin-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
        }

        /* Admin header layout */
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .admin-header-content {
            flex: 1;
            text-align: center;
        }

        .admin-header-nav {
            flex-shrink: 0;
        }

        .back-to-main-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .back-to-main-btn:hover {
            background-color: var(--ddb-yellow);
            color: var(--ddb-black);
            border-color: var(--ddb-yellow);
        }

        .back-to-main-btn svg {
            width: 1rem;
            height: 1rem;
        }

        .admin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        /* Admin Panel Base Styles */
        .admin-panel {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 2px 4px var(--shadow-color);
            transition: all 0.2s ease;
        }

        .admin-panel:hover {
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .panel-header {
            background-color: var(--bg-primary);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .panel-content {
            padding: 1.5rem;
        }

        /* Sync Overview Panel */
        .sync-mode-display {
            margin-bottom: 1.5rem;
        }

        .current-mode {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .mode-label {
            font-weight: 500;
            color: var(--text-secondary);
        }

        .mode-value {
            font-weight: 600;
            color: var(--ddb-yellow);
            font-size: 1.125rem;
        }

        .sync-status {
            margin-bottom: 1rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #6b7280;
            transition: background-color 0.2s;
        }

        .status-dot.active {
            background-color: #10b981;
        }

        .status-dot.warning {
            background-color: #f59e0b;
        }

        .status-dot.error {
            background-color: #ef4444;
        }

        .status-text {
            font-weight: 500;
            color: var(--text-primary);
        }

        .sync-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .stat-value {
            font-weight: 600;
            color: var(--text-primary);
        }

        .stat-value.health-healthy {
            color: #10b981;
        }

        .stat-value.health-warning {
            color: #f59e0b;
        }

        .stat-value.health-critical {
            color: #ef4444;
        }

        .stat-value.health-unknown {
            color: var(--text-secondary);
        }

        /* Sync Mode Selector */
        .mode-selector {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .mode-option {
            position: relative;
        }

        .mode-option input[type="radio"] {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }

        .mode-option .mode-label {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background-color: var(--bg-primary);
            border: 2px solid var(--border-color);
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .mode-option .mode-label:hover {
            border-color: var(--ddb-yellow);
            background-color: rgba(255, 209, 0, 0.05);
        }

        .mode-option input[type="radio"]:checked + .mode-label {
            border-color: var(--ddb-yellow);
            background-color: rgba(255, 209, 0, 0.1);
        }

        .mode-icon {
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .mode-details h4 {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 0.25rem 0;
        }

        .mode-details p {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin: 0;
        }

        .mode-actions {
            display: flex;
            gap: 0.75rem;
        }

        /* Action Buttons */
        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            text-align: center;
            transition: all 0.2s ease;
            position: relative;
            cursor: pointer;
        }

        .action-btn.primary {
            background-color: var(--ddb-yellow);
            color: var(--ddb-black);
        }

        .action-btn.primary:hover {
            background-color: #e6bc00;
            transform: translateY(-2px);
        }

        .action-btn.secondary {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .action-btn.secondary:hover {
            background-color: var(--bg-secondary);
            border-color: var(--ddb-yellow);
        }

        .action-btn.tertiary {
            background-color: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .action-btn.tertiary:hover {
            color: var(--text-primary);
            border-color: var(--text-secondary);
        }

        .action-btn.warning {
            background-color: #fef3c7;
            color: #d97706;
            border: 1px solid #f59e0b;
        }

        .action-btn.warning:hover {
            background-color: #fed7aa;
            border-color: #d97706;
            transform: translateY(-2px);
        }

        /* Hide reset sync errors button by default */
        #resetSyncErrorsBtn {
            display: none;
        }

        .action-icon {
            font-size: 1.5rem;
        }

        .action-text {
            font-size: 0.875rem;
        }

        .action-badge {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background-color: #ef4444;
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.125rem 0.375rem;
            border-radius: 9999px;
            min-width: 1.25rem;
            text-align: center;
        }

        /* Settings Panel */
        .settings-basic,
        .settings-advanced {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .setting-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .setting-item.checkbox-item {
            flex-direction: row;
            align-items: center;
            gap: 0.75rem;
        }

        .setting-label {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .checkbox-label {
            font-weight: 400;
            cursor: pointer;
        }

        .setting-select,
        .setting-input {
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .setting-select:focus,
        .setting-input:focus {
            outline: none;
            border-color: var(--ddb-yellow);
            box-shadow: 0 0 0 2px rgba(255, 209, 0, 0.2);
        }

        .setting-input-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .setting-input-group .setting-input {
            flex: 1;
        }

        .setting-unit {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .setting-help {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin: 0;
            font-style: italic;
        }

        .test-btn {
            padding: 0.375rem 0.75rem;
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            color: var(--text-primary);
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .test-btn:hover {
            background-color: var(--ddb-yellow);
            color: var(--ddb-black);
        }

        .settings-actions {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .toggle-btn {
            padding: 0.375rem 0.75rem;
            background-color: transparent;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            color: var(--text-secondary);
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .toggle-btn:hover {
            color: var(--text-primary);
            border-color: var(--ddb-yellow);
        }

        .refresh-btn {
            padding: 0.25rem 0.5rem;
            background-color: transparent;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .refresh-btn:hover {
            color: var(--text-primary);
            border-color: var(--ddb-yellow);
        }

        /* Activity Feed */
        .activity-feed {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            max-height: 500px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background-color: var(--bg-primary);
            border-radius: 0.375rem;
            border: 1px solid var(--border-color);
        }

        .activity-item.loading {
            color: var(--text-secondary);
            font-style: italic;
        }

        .activity-icon {
            font-size: 1.25rem;
            flex-shrink: 0;
        }

        .activity-text {
            flex: 1;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        /* Status Panel */
        .status-grid {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .status-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .status-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .status-bar {
            width: 100%;
            height: 8px;
            background-color: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
        }

        .status-progress {
            height: 100%;
            background-color: #10b981;
            transition: width 0.3s ease;
        }

        .status-progress.warning {
            background-color: #f59e0b;
        }

        .status-progress.critical {
            background-color: #ef4444;
        }

        .status-value {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .status-value.connection-connected {
            color: #10b981;
        }

        .status-value.connection-disconnected {
            color: #ef4444;
        }

        .status-value.connection-unknown {
            color: var(--text-secondary);
        }

        /* Responsive Design for Admin Dashboard */
        @media (max-width: 768px) {
            .admin-dashboard-container {
                padding: 1rem;
            }

            .admin-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .sync-stats {
                grid-template-columns: 1fr;
            }

            .mode-actions,
            .settings-actions {
                flex-direction: column;
            }

            .admin-title {
                font-size: 2rem;
            }

            /* Admin header mobile layout */
            .admin-header {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }

            .admin-header-nav {
                order: -1;
                display: flex;
                justify-content: center;
            }

            .back-to-main-btn {
                align-self: flex-start;
            }
        }

        @media (max-width: 480px) {
            .admin-dashboard {
                padding-top: 70px;
            }

            .panel-content {
                padding: 1rem;
            }

            .mode-option .mode-label {
                padding: 0.75rem;
            }

            .action-btn {
                padding: 0.75rem;
            }
        }

        /* Upload button animations */
        @keyframes blink-upload {
            0% { stroke: white; }
            50% { stroke: var(--ddb-yellow); }
            100% { stroke: white; }
        }

        .uploading svg {
            animation: blink-upload 0.4s infinite;
        }

        /* Icon button base styles */
        .icon-btn {
            position: relative;
            background-color: transparent;
            padding: 0.5rem;
            border: none;
        }

        .icon-btn svg {
            stroke: white;
            transition: stroke 0.2s;
        }

        .icon-btn:hover svg {
            stroke: var(--ddb-yellow);
        }

        .icon-btn::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: -45px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 50;
        }

        .icon-btn::before {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            border-width: 6px;
            border-style: solid;
            border-color: transparent transparent rgba(0, 0, 0, 0.9) transparent;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 51;
        }

        .icon-btn:hover::after,
        .icon-btn:hover::before {
            opacity: 1;
            visibility: visible;
        }

        /* Add pulsating animation */
        @keyframes pulse-blink {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(2);
                opacity: 0.5;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes pulse {
            0% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.6;
                transform: scale(1.05);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .blink {
            animation: pulse-blink 1.2s ease-in-out infinite;
        }

        /* Update upload status styles */
        .upload-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .upload-status .status-icon {
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background-color: var(--ddb-yellow);
            display: inline-block;
        }

        .upload-status .status-icon.blink {
            animation: pulse-blink 1.2s ease-in-out infinite;
        }

        .chat-message {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
        }

        .chat-message .main-content-area {
            line-height: 1.6;
        }

        .chat-message .main-content-area p {
            margin-bottom: 1.25rem;
        }

        .chat-message .main-content-area ul,
        .chat-message .main-content-area ol {
            margin-bottom: 1.25rem;
            padding-left: 1.5rem;
        }

        .chat-message .main-content-area li {
            margin-bottom: 0.5rem;
        }

        .chat-message .main-content-area h1,
        .chat-message .main-content-area h2,
        .chat-message .main-content-area h3,
        .chat-message .main-content-area h4 {
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .chat-message .main-content-area strong,
        .chat-message .main-content-area b {
            font-weight: 600;
        }

        .chat-message .main-content-area blockquote {
            margin: 1.25rem 0;
            padding-left: 1rem;
            border-left: 4px solid var(--ddb-yellow);
            color: var(--text-secondary);
        }

        /* Ensure proper spacing between sections */
        .chat-message .sources-section,
        .chat-message .prompts-section {
            margin-top: 2rem;
            padding-top: 1.5rem;
        }

        /* Streaming cursor animation */
        .streaming-cursor {
            color: var(--ddb-yellow);
            font-weight: bold;
            animation: cursor-blink 1s infinite;
            margin-left: 2px;
        }

        @keyframes cursor-blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Smooth text appearance during streaming */
        .main-content-area {
            transition: all 0.1s ease;
        }

        /* Welcome Screen Styles */
        .welcome-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 60vh;
            padding: 2rem;
            text-align: center;
        }

        .welcome-header {
            margin-bottom: 3rem;
        }

        .welcome-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin-bottom: 0;
        }

        .welcome-questions {
            width: 100%;
            max-width: 900px;
        }

        .questions-header {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2rem;
        }

        .questions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .question-card {
            background-color: var(--bg-primary);
            border: 2px solid var(--border-color);
            border-radius: 1rem;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            position: relative;
            overflow: hidden;
        }

        .question-card:hover {
            border-color: var(--ddb-yellow);
            box-shadow: 0 8px 25px var(--shadow-color);
            transform: translateY(-2px);
        }

        .question-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--ddb-yellow), #e6bc00);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .question-card:hover::before {
            opacity: 1;
        }

        .question-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            display: block;
        }

        .question-category {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
        }

        .question-text {
            font-size: 0.95rem;
            color: var(--text-secondary);
            line-height: 1.5;
            margin: 0;
        }

        /* Dark mode styles for welcome screen */
        [data-theme="dark"] .question-card {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .question-card:hover {
            background-color: var(--bg-primary);
            border-color: var(--ddb-yellow);
        }

        [data-theme="dark"] .welcome-title {
            color: var(--text-primary);
        }

        [data-theme="dark"] .welcome-subtitle {
            color: var(--text-secondary);
        }

        [data-theme="dark"] .questions-header {
            color: var(--text-primary);
        }

        [data-theme="dark"] .question-category {
            color: var(--text-primary);
        }

        [data-theme="dark"] .question-text {
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen">
        <div class="login-container">
            <img src="static/ddb-logo-login.png" alt="DDB Logo" class="login-logo" onerror="this.onerror=null; console.error('Failed to load login logo'); this.src='static/ddb-logo-80x80.png';">
            <h1 class="login-title">DDBrain</h1>
            <div class="space-y-4 mb-4">
                <div class="text-left text-gray-700 text-sm">
                    <p>Enter your Microsoft email below (optional hint):</p>
                </div>
                <input type="text" id="loginHintInput" class="login-input" placeholder="<EMAIL>" required>
            </div>
            <div class="mt-4">
                <a href="/login/microsoft" id="microsoftLoginBtn" class="block bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                    Sign in with Microsoft
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div id="mainContent">
        <header class="fixed-header">
            <div class="max-w-7xl mx-auto flex justify-between items-center">
                <div class="flex items-center">
                    <button class="sidebar-toggle" id="sidebarToggle" style="padding: 0.25rem; margin-right: 0.25rem;">
                        <svg viewBox="0 0 24 24" class="w-6 h-6">
                            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                        </svg>
                    </button>
                    <div class="burger-menu" id="burgerMenu" style="margin-right: 0.25rem; padding: 0.25rem;">
                        <div></div>
                        <div></div>
                        <div></div>
                    </div>
                    <div class="logo-container" style="gap: 0.25rem;">
                        <img src="/static/ddb-logo-80x80.png" alt="DDB Logo" class="h-8 w-8">
                        <span class="text-xl font-bold" style="color: var(--ddb-yellow)">DDBrain</span>
                    </div>
                </div>
                <div class="flex items-center gap-1">
                    {% if is_admin %}
                    <button id="adminTabBtn" class="admin-tab-btn" data-tooltip="Admin Dashboard">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </button>
                    {% endif %}
                    <button id="darkModeToggle" class="icon-btn" data-tooltip="Toggle Dark Mode">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path class="sun-icon hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                            <path class="moon-icon" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                        </svg>
                    </button>
                    <button id="newChatBtn" class="icon-btn" data-tooltip="New Chat">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                        </svg>
                    </button>
                    <button id="logoutBtn" class="icon-btn" data-tooltip="Logout">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <main class="content-wrapper">
            <aside id="sidebar">
                <div class="mb-6">
                    <h2 class="text-xl font-semibold mb-2">DDB Knowledge Base</h2>
                    <p class="text-sm mb-4" style="color: var(--text-secondary);">Company policies, procedures, and documentation</p>
                    
                    <!-- Simple Sync Status Indicator -->
                    <div id="syncStatus" class="mb-4 p-3 bg-gray-100 rounded border" style="background-color: var(--bg-secondary); border-color: var(--border-color);">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium" style="color: var(--text-primary);">Document Sync</span>
                            <div id="syncIndicator" class="flex items-center gap-1">
                                <div id="syncDot" class="w-2 h-2 rounded-full bg-gray-400"></div>
                                <span id="syncStatusText" class="text-xs" style="color: var(--text-secondary);">Unknown</span>
                            </div>
                        </div>
                        <div id="lastSyncTime" class="text-xs mt-1" style="color: var(--text-secondary);">
                            Last sync: Never
                        </div>
                    </div>
                </div>
                <div id="documentList" class="space-y-4">
                    <!-- Document items will be dynamically added here -->
                </div>
            </aside>

            <section id="chatContainer">
                <div id="chatMessages" class="flex-1 overflow-y-auto mb-4 space-y-4">
                    <!-- Welcome Screen -->
                    <div id="welcomeScreen" class="welcome-container">
                        <div class="welcome-header">
                            <div class="welcome-logo">
                                <img id="welcomeLogo" src="/static/ddb-logo-login.png" alt="DDB Logo" class="w-16 h-16 mx-auto mb-4">
                            </div>
                            <h2 class="welcome-title">Welcome to DDBrain</h2>
                            <p class="welcome-subtitle">Your AI-powered assistant for company information and support</p>
                        </div>
                        
                        <div class="welcome-questions">
                            <h3 class="questions-header">Try asking about:</h3>
                            <div class="questions-grid">
                                <div class="question-card" data-category="AI Policy Basics">
                                    <div class="question-icon">🤖</div>
                                    <h4 class="question-category">AI Policy Basics</h4>
                                    <p class="question-text">What are DDB's guidelines for using AI technologies in internal operations?</p>
                                </div>
                                
                                <div class="question-card" data-category="Client-Facing AI Work">
                                    <div class="question-icon">🎯</div>
                                    <h4 class="question-category">Client-Facing AI Work</h4>
                                    <p class="question-text">Can DDB use AI-generated content in client presentations or deliverables?</p>
                                </div>
                                
                                <div class="question-card" data-category="Employee Conduct">
                                    <div class="question-icon">⚖️</div>
                                    <h4 class="question-category">Employee Conduct</h4>
                                    <p class="question-text">What disciplinary actions can DDB take for violations of company rules?</p>
                                </div>
                                
                                <div class="question-card" data-category="Filing for Leaves">
                                    <div class="question-icon">📅</div>
                                    <h4 class="question-category">Filing for Leaves</h4>
                                    <p class="question-text">What are DDB's policies on Leaves?</p>
                                </div>
                                
                                <div class="question-card" data-category="Mental Health Support">
                                    <div class="question-icon">💚</div>
                                    <h4 class="question-category">Mental Health Support</h4>
                                    <p class="question-text">How does DDB support employee mental health in the workplace?</p>
                                </div>
                                
                                <div class="question-card" data-category="Creative Philosophy">
                                    <div class="question-icon">✨</div>
                                    <h4 class="question-category">Creative Philosophy</h4>
                                    <p class="question-text">What does DDB mean by "The Power to Move" and how does it shape their approach to creativity?</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <form id="queryForm" class="chat-input-container">
                    <input type="text" id="queryInput" class="input-field" placeholder="Ask a question...">
                    <button type="submit" id="sendButton" class="btn-primary">Send</button>
                </form>
            </section>
        </main>

        <!-- Admin Dashboard Section -->
        <section id="adminDashboard" class="admin-dashboard" style="display: none;">
            <div class="admin-dashboard-container">
                <div class="admin-header">
                    <div class="admin-header-content">
                        <h1 class="admin-title">🛠️ Admin Dashboard</h1>
                        <p class="admin-subtitle">Manage document synchronization and system settings</p>
                    </div>
                    <div class="admin-header-nav">
                        <button id="backToMainBtn" class="back-to-main-btn" onclick="toggleAdminDashboard()">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                            </svg>
                            Back to Main App
                        </button>
                    </div>
                </div>

                <div class="admin-grid">
                    <!-- Sync Overview Panel -->
                    <div class="admin-panel sync-overview-panel">
                        <div class="panel-header">
                            <h3 class="panel-title"><svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/></svg>Sync Overview</h3>
                        </div>
                        <div class="panel-content">
                            <div class="sync-mode-display">
                                <div class="current-mode">
                                    <span class="mode-label">Current Mode:</span>
                                    <span id="currentSyncMode" class="mode-value">Loading...</span>
                                </div>
                                <div class="sync-status">
                                    <div class="status-indicator">
                                        <div id="adminSyncDot" class="status-dot"></div>
                                        <span id="adminSyncStatus" class="status-text">Checking...</span>
                                    </div>
                                </div>
                            </div>
                            <div class="sync-stats">
                                <div class="stat-item">
                                    <span class="stat-label">Last sync:</span>
                                    <span id="adminLastSync" class="stat-value">Never</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Next sync:</span>
                                    <span id="adminNextSync" class="stat-value">-</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Health status:</span>
                                    <span id="adminHealthStatus" class="stat-value health-unknown">Unknown</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sync Mode Selector Panel -->
                    <div class="admin-panel sync-mode-panel">
                        <div class="panel-header">
                            <h3 class="panel-title"><svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/></svg>Sync Mode</h3>
                        </div>
                        <div class="panel-content">
                            <div class="mode-selector">
                                <div class="mode-option" data-mode="manual">
                                    <input type="radio" id="modeManual" name="syncMode" value="manual">
                                    <label for="modeManual" class="mode-label">
                                        <div class="mode-icon"><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/></svg></div>
                                        <div class="mode-details">
                                            <h4 class="mode-name">Manual Only</h4>
                                            <p class="mode-description">Admin manually syncs when needed</p>
                                        </div>
                                    </label>
                                </div>
                                
                                <div class="mode-option" data-mode="monitoring">
                                    <input type="radio" id="modeMonitoring" name="syncMode" value="monitoring">
                                    <label for="modeMonitoring" class="mode-label">
                                        <div class="mode-icon"><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/></svg></div>
                                        <div class="mode-details">
                                            <h4 class="mode-name">Smart Monitoring</h4>
                                            <p class="mode-description">Watches for changes, alerts admin</p>
                                        </div>
                                    </label>
                                </div>
                                
                                <div class="mode-option" data-mode="auto-import">
                                    <input type="radio" id="modeAutoImport" name="syncMode" value="auto-import">
                                    <label for="modeAutoImport" class="mode-label">
                                        <div class="mode-icon"><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/></svg></div>
                                        <div class="mode-details">
                                            <h4 class="mode-name">Auto-Import New Files</h4>
                                            <p class="mode-description">Automatically adds new documents</p>
                                        </div>
                                    </label>
                                </div>
                                
                                <div class="mode-option" data-mode="full-auto">
                                    <input type="radio" id="modeFullAuto" name="syncMode" value="full-auto">
                                    <label for="modeFullAuto" class="mode-label">
                                        <div class="mode-icon"><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"/></svg></div>
                                        <div class="mode-details">
                                            <h4 class="mode-name">Full Automation</h4>
                                            <p class="mode-description">Handles everything automatically</p>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            <div class="mode-actions">
                                <button id="saveSyncMode" class="btn-primary"><svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>Save Changes</button>
                                <button id="resetSyncMode" class="btn-secondary"><svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/></svg>Reset to Safe</button>
                            </div>
                        </div>
                    </div>

                    <!-- Activity Feed Panel -->
                    <div class="admin-panel activity-panel">
                        <div class="panel-header">
                            <h3 class="panel-title"><svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/></svg>Recent Activity</h3>
                            <button id="refreshActivity" class="refresh-btn"><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/></svg></button>
                        </div>
                        <div class="panel-content">
                            <div id="activityFeed" class="activity-feed">
                                <div class="activity-item loading">
                                    <span class="activity-icon">⏳</span>
                                    <span class="activity-text">Loading recent activity...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions Panel -->
                    <div class="admin-panel actions-panel">
                        <div class="panel-header">
                            <h3 class="panel-title"><svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/></svg>Quick Actions</h3>
                        </div>
                        <div class="panel-content">
                            <div class="actions-grid">
                                <button id="syncNowBtn" class="action-btn primary">
                                    <span class="action-icon"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/></svg></span>
                                    <span class="action-text">Sync Everything Now</span>
                                </button>
                                
                                <button id="importPendingBtn" class="action-btn secondary">
                                    <span class="action-icon"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/></svg></span>
                                    <span class="action-text">Import Pending Files</span>
                                    <span id="pendingCount" class="action-badge">0</span>
                                </button>
                                
                                <button id="cleanupBtn" class="action-btn secondary">
                                    <span class="action-icon"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/></svg></span>
                                    <span class="action-text">Clean Up Removed Files</span>
                                    <span id="cleanupCount" class="action-badge">0</span>
                                </button>
                                
                                <button id="viewReportsBtn" class="action-btn tertiary">
                                    <span class="action-icon"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/></svg></span>
                                    <span class="action-text">View Sync Reports</span>
                                </button>
                                
                                <button id="indexCleanupBtn" class="action-btn tertiary">
                                    <span class="action-icon"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/></svg></span>
                                    <span class="action-text">Fix Index Corruption</span>
                                </button>
                                
                                <button id="resetSyncErrorsBtn" class="action-btn warning">
                                    <span class="action-icon"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"/></svg></span>
                                    <span class="action-text">Reset Sync Errors</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Panel -->
                    <div class="admin-panel settings-panel">
                        <div class="panel-header">
                            <h3 class="panel-title"><svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/></svg>Settings</h3>
                            <button id="advancedToggle" class="toggle-btn">Show Advanced</button>
                        </div>
                        <div class="panel-content">
                            <div class="settings-basic">
                                <div class="setting-item">
                                    <label class="setting-label">How often to check for changes:</label>
                                    <select id="syncInterval" class="setting-select">
                                        <option value="5">Every 5 minutes (More responsive)</option>
                                        <option value="15" selected>Every 15 minutes (Recommended)</option>
                                        <option value="30">Every 30 minutes</option>
                                        <option value="60">Every hour (Less frequent)</option>
                                    </select>
                                </div>
                                
                                <div class="setting-item">
                                    <label class="setting-label">File size limit for auto-import:</label>
                                    <div class="setting-input-group">
                                        <input type="number" id="fileSizeLimit" class="setting-input" value="10" min="1" max="100">
                                        <span class="setting-unit">MB</span>
                                    </div>
                                    <p class="setting-help">Protects against importing huge files automatically</p>
                                </div>
                                
                                <div class="setting-item">
                                    <label class="setting-label">Daily import limit:</label>
                                    <div class="setting-input-group">
                                        <input type="number" id="dailyLimit" class="setting-input" value="50" min="1" max="1000">
                                        <span class="setting-unit">files</span>
                                    </div>
                                    <p class="setting-help">Prevents system overload from processing too many files</p>
                                </div>
                                
                                <div class="setting-item">
                                    <label class="setting-label">Email notifications:</label>
                                    <div class="setting-input-group">
                                        <input type="email" id="notificationEmail" class="setting-input" placeholder="<EMAIL>, <EMAIL>">
                                        <button id="testEmail" class="test-btn">Test</button>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="advancedSettings" class="settings-advanced" style="display: none;">
                                <div class="setting-item">
                                    <label class="setting-label">Error threshold:</label>
                                    <div class="setting-input-group">
                                        <input type="number" id="errorThreshold" class="setting-input" value="5" min="1" max="50">
                                        <span class="setting-unit">consecutive errors</span>
                                    </div>
                                    <p class="setting-help">System will disable automatic sync after this many consecutive errors</p>
                                </div>
                                
                                <div class="setting-item checkbox-item">
                                    <input type="checkbox" id="requireApproval" checked>
                                    <label for="requireApproval" class="checkbox-label">Require manual approval for deletions</label>
                                </div>
                            </div>
                            
                            <div class="settings-actions">
                                <button id="saveSettings" class="btn-primary"><svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>Save Settings</button>
                                <button id="resetSettings" class="btn-secondary"><svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/></svg>Reset to Defaults</button>
                            </div>
                        </div>
                    </div>

                    <!-- System Status Panel -->
                    <div class="admin-panel status-panel">
                        <div class="panel-header">
                            <h3 class="panel-title"><svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/></svg>System Status</h3>
                        </div>
                        <div class="panel-content">
                            <div class="status-grid">
                                <div class="status-item">
                                    <span class="status-label">Disk Space:</span>
                                    <div class="status-bar">
                                        <div id="diskUsageBar" class="status-progress"></div>
                                    </div>
                                    <span id="diskUsageText" class="status-value">Checking...</span>
                                </div>
                                
                                <div class="status-item">
                                    <span class="status-label">Memory Usage:</span>
                                    <div class="status-bar">
                                        <div id="memoryUsageBar" class="status-progress"></div>
                                    </div>
                                    <span id="memoryUsageText" class="status-value">Checking...</span>
                                </div>
                                
                                <div class="status-item">
                                    <span class="status-label">SharePoint Connection:</span>
                                    <span id="sharepointStatus" class="status-value connection-unknown">Testing...</span>
                                </div>
                                
                                <div class="status-item">
                                    <span class="status-label">Background Sync:</span>
                                    <span id="backgroundSyncStatus" class="status-value">Checking...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script>
        // Health check configuration
        const HEALTH_CHECK_INTERVAL = 60000; // Check every minute
        let healthCheckTimer = null;

        // Start health checks after successful login
        function startHealthChecks() {
            healthCheckTimer = setInterval(async () => {
                try {
                    const response = await fetch(`${window.__RAG_BASE}/health`);
                    if (!response.ok) {
                        console.warn('Health check failed, server might be inactive');
                    }
                } catch (error) {
                    console.error('Health check failed:', error);
                    // Server might have shut down, show reconnection message
                    showReconnectionMessage();
                }
            }, HEALTH_CHECK_INTERVAL);
        }

        // Show reconnection message
        function showReconnectionMessage() {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'fixed top-0 left-0 w-full bg-yellow-100 text-yellow-800 px-4 py-3 text-center';
            messageDiv.innerHTML = `
                Server has shut down due to inactivity. 
                <button onclick="window.location.reload()" class="underline ml-2">
                    Click here to reconnect
                </button>
            `;
            document.body.prepend(messageDiv);
        }

        // Stop health checks on logout
        function stopHealthChecks() {
            if (healthCheckTimer) {
                clearInterval(healthCheckTimer);
                healthCheckTimer = null;
            }
        }

        // Login handling - Modified for Microsoft login hint
        // Remove the basic auth form submit listener
        // document.getElementById('loginForm').addEventListener('submit', async (e) => { ... });

        // Add listener to the Microsoft Login Button
        document.getElementById('microsoftLoginBtn').addEventListener('click', (e) => {
            e.preventDefault(); // Prevent default link behavior
            const hintInput = document.getElementById('loginHintInput');
            const loginHint = hintInput.value.trim();
            let redirectUrl = "/login/microsoft";

            if (loginHint) {
                redirectUrl += `?login_hint=${encodeURIComponent(loginHint)}`;
                console.log(`Microsoft login initiated with hint: ${loginHint}`);
            } else {
                console.log("Microsoft login initiated without hint.");
            }
            
            window.location.href = window.__RAG_BASE + redirectUrl; // Redirect to the constructed URL
        });

        // Document management
        // Removed upload-related event listeners

        // Add sidebar toggle functionality
        const sidebarToggle = document.getElementById('sidebarToggle');
        const burgerMenu = document.getElementById('burgerMenu');
        const contentWrapper = document.querySelector('.content-wrapper');
        const sidebar = document.getElementById('sidebar');

        function toggleSidebar() {
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('mobile-visible');
            } else {
                contentWrapper.classList.toggle('sidebar-hidden');
            }
        }

        sidebarToggle.addEventListener('click', toggleSidebar);
        burgerMenu.addEventListener('click', toggleSidebar);

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                sidebar.classList.remove('mobile-visible');
            }
        });

        // Update loadDocuments function to handle SharePoint documents and sync status
        async function loadDocuments() {
            const documentList = document.getElementById('documentList');
            documentList.innerHTML = ''; // Set to empty instead of showing loading text
            
            try {
                const response = await fetch(`${window.__RAG_BASE}/api/documents`, { credentials: 'include' });
                if (!response.ok) {
                    throw new Error('Failed to fetch documents');
                }
                const data = await response.json();
                
                // Update sync status indicators
                updateSyncStatus(data.sync_info);
                
                if (data.documents && data.documents.length > 0) {
                    // Sort documents alphabetically by file name
                    const sortedDocs = data.documents.sort((a, b) => {
                        const nameA = (a.file_name || '').toLowerCase();
                        const nameB = (b.file_name || '').toLowerCase();
                        return nameA.localeCompare(nameB);
                    });

                    // Remove duplicates based on sharepoint_id
                    const uniqueDocs = sortedDocs.filter((doc, index, self) =>
                        index === self.findIndex((d) => (
                            d.sharepoint_id === doc.sharepoint_id
                        ))
                    );
                    
                    // Clear list before adding items
                    documentList.innerHTML = ''; 
                    
                    uniqueDocs.forEach(doc => {
                        const listItem = document.createElement('div');
                        listItem.className = 'document-item p-3 border border-gray-200 rounded mb-2';
                        const fileName = doc.file_name || `Document ID: ${doc.doc_id}` || 'Unnamed Document';
                        
                        // Add sync status indicator for SharePoint documents
                        const fileContainer = document.createElement('div');
                        fileContainer.className = 'flex items-center justify-between';
                        
                        const fileInfo = document.createElement('div');
                        fileInfo.className = 'flex-1';
                        
                        // Create link to view document through our app if it has a doc_id
                        if (doc.doc_id) {
                            const link = document.createElement('a');
                            link.href = `${window.__RAG_BASE}/api/documents/${encodeURIComponent(doc.doc_id)}/view`;
                            link.textContent = fileName;
                            link.target = '_blank';
                            link.className = 'text-blue-600 hover:text-blue-800';
                            fileInfo.appendChild(link);
                        } else {
                            fileInfo.textContent = fileName;
                        }
                        
                        fileContainer.appendChild(fileInfo);
                        
                        // Add sync status indicator
                        if (doc.has_sharepoint_id) {
                            const syncBadge = document.createElement('div');
                            syncBadge.className = 'flex items-center gap-1';
                            
                            const syncDot = document.createElement('div');
                            syncDot.className = 'w-2 h-2 rounded-full';
                            
                            const syncText = document.createElement('span');
                            syncText.className = 'text-xs';
                            
                            // Set status colors
                            if (doc.sync_status === 'local_only') {
                                syncDot.className += ' bg-gray-400';
                                syncText.textContent = 'Local';
                                syncText.style.color = 'var(--text-secondary)';
                            } else {
                                syncDot.className += ' bg-blue-400';
                                
                                // Extract meaningful category from document name/type
                                let folderName = 'SharePoint'; // fallback
                                
                                // Create smart categorization based on document name patterns
                                const fileName = (doc.file_name || '').toLowerCase();
                                
                                if (fileName.includes('employee') || fileName.includes('handbook') || fileName.includes('hr') || fileName.includes('policy')) {
                                    folderName = 'HR';
                                } else if (fileName.includes('news') || fileName.includes('newsletter') || fileName.includes('communication')) {
                                    folderName = 'News';
                                } else if (fileName.includes('report') || fileName.includes('form') || fileName.includes('request')) {
                                    folderName = 'Forms';
                                } else if (fileName.includes('training') || fileName.includes('learning') || fileName.includes('development') || fileName.includes('thinkpiece')) {
                                    folderName = 'Training';
                                } else if (fileName.includes('agile') || fileName.includes('intelligence') || fileName.includes('strategy')) {
                                    folderName = 'Strategy';
                                } else if (fileName.includes('leadership') || fileName.includes('principle')) {
                                    folderName = 'Leadership';
                                } else if (fileName.includes('casting') || fileName.includes('chemistry') || fileName.includes('silence') || fileName.includes('evolution') || fileName.includes('optimising') || fileName.includes('toxicity') || fileName.includes('relationship')) {
                                    folderName = 'TBS';
                                } else {
                                    // Try to extract from source if available
                                    if (doc.source && doc.source.includes('sharepoint')) {
                                        folderName = 'SharePoint';
                                    } else {
                                        folderName = 'Documents';
                                    }
                                }
                                
                                // Truncate if too long for UI
                                if (folderName.length > 12) {
                                    folderName = folderName.substring(0, 9) + '...';
                                }
                                
                                syncText.textContent = folderName;
                                syncText.style.color = 'var(--text-secondary)';
                            }
                            
                            syncBadge.appendChild(syncDot);
                            syncBadge.appendChild(syncText);
                            fileContainer.appendChild(syncBadge);
                        }
                        
                        listItem.appendChild(fileContainer);
                        documentList.appendChild(listItem);
                    });
                } else {
                    documentList.innerHTML = '<p class="text-gray-500 text-center py-4">No documents indexed yet. Use the "Browse SharePoint" button to import documents.</p>';
                }
            } catch (error) {
                console.error('Error loading documents:', error);
                documentList.innerHTML = '<p class="text-red-500 text-center py-4">Error loading documents. Please try again later.</p>';
            }
        }

        // Function to update sync status indicators
        function updateSyncStatus(syncInfo) {
            const syncDot = document.getElementById('syncDot');
            const syncStatusText = document.getElementById('syncStatusText');
            const lastSyncTime = document.getElementById('lastSyncTime');
            
            if (!syncInfo) return;
            
            // Update sync indicator based on configuration and status
            if (syncInfo.sharepoint_configured && syncInfo.auto_sync_enabled) {
                syncDot.className = 'w-2 h-2 rounded-full bg-green-400';
                syncStatusText.textContent = 'Active';
            } else if (syncInfo.sharepoint_configured) {
                syncDot.className = 'w-2 h-2 rounded-full bg-yellow-400';
                syncStatusText.textContent = 'Manual';
            } else {
                syncDot.className = 'w-2 h-2 rounded-full bg-gray-400';
                syncStatusText.textContent = 'Disabled';
            }
            
            // Update last sync time
            if (syncInfo.last_sync_time) {
                const syncDate = new Date(syncInfo.last_sync_time);
                const now = new Date();
                const diffMinutes = Math.floor((now - syncDate) / (1000 * 60));
                
                let timeText;
                if (diffMinutes < 1) {
                    timeText = 'Just now';
                } else if (diffMinutes < 60) {
                    timeText = `${diffMinutes}m ago`;
                } else if (diffMinutes < 1440) {
                    timeText = `${Math.floor(diffMinutes / 60)}h ago`;
                } else {
                    timeText = syncDate.toLocaleDateString();
                }
                
                lastSyncTime.textContent = `Last sync: ${timeText}`;
            } else {
                lastSyncTime.textContent = 'Last sync: Never';
            }
        }

        // Add clear documents functionality
        document.addEventListener('DOMContentLoaded', () => {
            const clearBtn = document.getElementById('clearDocumentsBtn');
            if (clearBtn) {
                clearBtn.addEventListener('click', async () => {
                    if (confirm('Are you sure you want to clear all documents? This cannot be undone.')) {
                        try {
                            const response = await fetch(`${window.__RAG_BASE}/api/documents/clear`, {
                                method: 'POST',
                                credentials: 'include'
                            });
                            
                            if (response.ok) {
                                loadDocuments(); // Refresh the document list
                            } else {
                                throw new Error('Failed to clear documents');
                            }
                        } catch (error) {
                            console.error('Error clearing documents:', error);
                            alert('Failed to clear documents. Please try again.');
                        }
                    }
                });
            }

            // Add manual sync functionality
            const manualSyncBtn = document.getElementById('manualSyncBtn');
            if (manualSyncBtn) {
                manualSyncBtn.addEventListener('click', async () => {
                    const originalText = manualSyncBtn.textContent;
                    manualSyncBtn.textContent = 'Syncing...';
                    manualSyncBtn.disabled = true;
                    
                    try {
                        const response = await fetch(`${window.__RAG_BASE}/api/sync/manual`, {
                            method: 'POST',
                            credentials: 'include'
                        });
                        
                        if (response.ok) {
                            const result = await response.json();
                            console.log('Sync completed:', result);
                            
                            // Show success message
                            let message = `Sync completed successfully!\n\n`;
                            message += `SharePoint files found: ${result.sharepoint_files_found}\n`;
                            if (result.deleted_count > 0) {
                                message += `Removed ${result.deleted_count} deleted files from index\n`;
                            }
                            if (result.errors && result.errors.length > 0) {
                                message += `\nWarnings: ${result.errors.length} items had issues`;
                            }
                            
                            alert(message);
                            
                            // Refresh the document list to show updated status
                            loadDocuments();
                        } else {
                            const error = await response.json();
                            throw new Error(error.detail || 'Failed to sync documents');
                        }
                    } catch (error) {
                        console.error('Error syncing documents:', error);
                        alert(`Failed to sync documents: ${error.message}\n\nPlease try again or check your SharePoint access.`);
                    } finally {
                        manualSyncBtn.textContent = originalText;
                        manualSyncBtn.disabled = false;
                    }
                });
            }
        });

        // Admin Dashboard Functionality
        let adminDashboardVisible = false;
        let adminData = {};

        // Initialize admin dashboard
        function initializeAdminDashboard() {
            setupAdminEventHandlers();
            startAdminDataPolling();
            // Don't load data immediately - wait until dashboard is opened
        }

        // Toggle admin dashboard visibility
        function toggleAdminDashboard() {
            const adminDashboard = document.getElementById('adminDashboard');
            const adminTabBtn = document.getElementById('adminTabBtn');
            const mainContent = document.querySelector('.content-wrapper');
            
            if (!adminDashboard || !adminTabBtn) return;

            adminDashboardVisible = !adminDashboardVisible;
            
            if (adminDashboardVisible) {
                adminDashboard.style.display = 'block';
                adminTabBtn.classList.add('active');
                if (mainContent) mainContent.style.display = 'none';
                
                // Initialize server time sync before loading data
                synchronizeWithServerTime();
                loadAdminData(); // Refresh data when opening
                startAdminDataRefresh(); // Start auto-refresh
            } else {
                adminDashboard.style.display = 'none';
                adminTabBtn.classList.remove('active');
                if (mainContent) mainContent.style.display = 'flex';
                stopAdminDataRefresh(); // Stop auto-refresh
                stopSyncCountdownTimer(); // Stop countdown timer
            }
        }

        // Auto-refresh interval for admin data
        let adminDataRefreshInterval = null;
        
        // Start auto-refresh for admin data
        function startAdminDataRefresh() {
            // Clear any existing interval
            if (adminDataRefreshInterval) {
                clearInterval(adminDataRefreshInterval);
            }
            
            // Immediate refresh on start
            if (adminTabBtn.classList.contains('active')) {
                loadAdminData();
            }
            
            // Refresh every 15 seconds (more frequent for Render)
            adminDataRefreshInterval = setInterval(() => {
                if (adminTabBtn.classList.contains('active')) {
                    console.log('Auto-refreshing admin data...');
                    loadAdminData();
                }
            }, 15000);
        }
        
        // Stop auto-refresh for admin data
        function stopAdminDataRefresh() {
            if (adminDataRefreshInterval) {
                clearInterval(adminDataRefreshInterval);
                adminDataRefreshInterval = null;
            }
        }

        // Load admin dashboard data with improved error handling
        async function loadAdminData() {
            try {
                let hasErrors = false;
                const errors = [];

                // Load sync status
                try {
                    const statusResponse = await fetch(`${window.__RAG_BASE}/api/sync/background-status`, { 
                        credentials: 'include' 
                    });
                    if (statusResponse.ok) {
                        adminData.syncStatus = await statusResponse.json();
                        console.log('🔍 RENDER DEBUG - Received sync status:', adminData.syncStatus);
                        console.log('🔍 Last sync time from API:', adminData.syncStatus?.sync_info?.last_sync_time);
                        console.log('🔍 Debug raw sync time:', adminData.syncStatus?.sync_info?.debug_sync_time_raw);
                        updateAdminSyncOverview(adminData.syncStatus);
                        updateResetSyncErrorsButton(adminData.syncStatus);
                    } else if (statusResponse.status === 401) {
                        // Authentication issue - don't show persistent error
                        console.warn('Admin data loading failed: Not authenticated');
                        return;
                    } else {
                        errors.push(`Sync status: ${statusResponse.status} ${statusResponse.statusText}`);
                        hasErrors = true;
                    }
                } catch (err) {
                    errors.push(`Sync status: ${err.message}`);
                    hasErrors = true;
                }

                // Load notifications
                try {
                    const notificationsResponse = await fetch(`${window.__RAG_BASE}/api/sync/notifications`, { 
                        credentials: 'include' 
                    });
                    if (notificationsResponse.ok) {
                        adminData.notifications = await notificationsResponse.json();
                        updateAdminActivityFeed(adminData.notifications);
                    } else if (notificationsResponse.status !== 401) {
                        errors.push(`Notifications: ${notificationsResponse.status} ${notificationsResponse.statusText}`);
                        hasErrors = true;
                    }
                } catch (err) {
                    errors.push(`Notifications: ${err.message}`);
                    hasErrors = true;
                }

                // Load settings
                try {
                    const settingsResponse = await fetch(`${window.__RAG_BASE}/api/sync/settings`, { 
                        credentials: 'include' 
                    });
                    if (settingsResponse.ok) {
                        adminData.settings = await settingsResponse.json();
                        updateAdminSettings(adminData.settings);
                    } else if (settingsResponse.status !== 401) {
                        errors.push(`Settings: ${settingsResponse.status} ${settingsResponse.statusText}`);
                        hasErrors = true;
                    }
                } catch (err) {
                    errors.push(`Settings: ${err.message}`);
                    hasErrors = true;
                }

                // Only show error if there are actual issues (not auth problems)
                if (hasErrors && errors.length > 0) {
                    console.error('Admin data loading errors:', errors);
                    showAdminError('Some admin data failed to load: ' + errors.join(', '));
                }

            } catch (error) {
                console.error('Critical error loading admin data:', error);
                showAdminError('Failed to load admin data: ' + error.message);
            }
        }

        // Update sync overview panel
        function updateAdminSyncOverview(data) {
            const { sync_info, enhanced_sync_config, performance_metrics, health_assessment } = data;

            // Update current mode display
            const currentMode = getCurrentSyncModeDisplay(enhanced_sync_config);
            const currentModeEl = document.getElementById('currentSyncMode');
            if (currentModeEl) currentModeEl.textContent = currentMode;

            // Update sync status
            const statusDot = document.getElementById('adminSyncDot');
            const statusText = document.getElementById('adminSyncStatus');
            if (statusDot && statusText) {
                statusDot.className = 'status-dot ' + getStatusClass(health_assessment.status);
                statusText.textContent = health_assessment.status === 'healthy' ? 'Working Well' : 
                                        health_assessment.status === 'warning' ? 'Needs Attention' :
                                        health_assessment.status === 'critical' ? 'Issues Detected' : 'Unknown';
            }

            // Update last sync time with timezone handling
            const lastSyncEl = document.getElementById('adminLastSync');
            if (lastSyncEl && sync_info.last_sync_time) {
                try {
                    // Use server-synchronized time for accurate calculation
                    const syncTime = new Date(sync_info.last_sync_time);
                    console.log('🕐 Parsed sync time:', syncTime, 'from:', sync_info.last_sync_time);
                    
                    // Calculate time ago using server-corrected time
                    const now = getCorrectedTime();
                    const timeDiff = now - syncTime;
                    const minutes = Math.floor(timeDiff / 60000);
                    
                    let timeText;
                    if (minutes < 1) {
                        timeText = 'Just now';
                    } else if (minutes < 60) {
                        timeText = `${minutes} minute${minutes === 1 ? '' : 's'} ago`;
                    } else {
                        const hours = Math.floor(minutes / 60);
                        timeText = `${hours} hour${hours === 1 ? '' : 's'} ago`;
                    }
                    
                    lastSyncEl.textContent = timeText;
                } catch (error) {
                    console.error('❌ Error parsing sync time:', error, sync_info.last_sync_time);
                    lastSyncEl.textContent = 'Error parsing time';
                }
            } else if (lastSyncEl) {
                lastSyncEl.textContent = 'Never';
            }
            
            // Update next sync countdown
            updateNextSyncCountdown(sync_info);
            
            // Start countdown timer if not already running
            startSyncCountdownTimer(sync_info);

            // Update health status
            const healthStatusEl = document.getElementById('adminHealthStatus');
            if (healthStatusEl) {
                healthStatusEl.textContent = health_assessment.status;
                healthStatusEl.className = 'stat-value health-' + health_assessment.status;
            }

            // Update action buttons with pending counts
            updateActionButtonCounts(data);

            // Update system status bars
            updateSystemStatus(data.resource_status);
        }

        // Update reset sync errors button visibility
        function updateResetSyncErrorsButton(data) {
            const resetSyncErrorsBtn = document.getElementById('resetSyncErrorsBtn');
            if (!resetSyncErrorsBtn) return;
            
            const { sync_info } = data;
            
            // Show button if sync is disabled due to errors
            const syncDisabled = sync_info?.sync_disabled === true || 
                               sync_info?.status === 'disabled' ||
                               sync_info?.consecutive_errors >= 5;
            
            if (syncDisabled) {
                resetSyncErrorsBtn.style.display = 'flex';
                resetSyncErrorsBtn.title = 'Background sync is disabled due to errors. Click to reset and re-enable sync.';
            } else {
                resetSyncErrorsBtn.style.display = 'none';
            }
        }

        // Get display text for current sync mode
        function getCurrentSyncModeDisplay(config) {
            if (!config.auto_import_enabled && !config.auto_delete_enabled) {
                return config.auto_sync_enabled ? 'Smart Monitoring' : 'Manual Only';
            } else if (config.auto_import_enabled && !config.auto_delete_enabled) {
                return 'Auto-Import New Files';
            } else if (config.auto_import_enabled && config.auto_delete_enabled) {
                return 'Full Automation';
            }
            return 'Custom Configuration';
        }

        // Get status CSS class
        function getStatusClass(status) {
            switch (status) {
                case 'healthy': return 'active';
                case 'warning': return 'warning';
                case 'critical': return 'error';
                default: return '';
            }
        }

        // Format time ago
        function formatTimeAgo(date) {
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
            if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
            return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
        }
        
        // Sync countdown timer and server time synchronization
        let syncCountdownInterval = null;
        let serverTimeOffset = 0;
        let lastServerSync = 0;
        
        // Function to synchronize with server time
        function synchronizeWithServerTime() {
            const clientTime = new Date().getTime();
            
            fetch(`${window.__RAG_BASE}/api/time`, { credentials: 'include' })
                .then(response => response.json())
                .then(data => {
                    const serverTime = new Date(data.server_time).getTime();
                    serverTimeOffset = serverTime - clientTime;
                    lastServerSync = clientTime;
                    console.log(`⏰ Server time synchronized. Offset: ${serverTimeOffset}ms`);
                    
                    // Refresh sync status with corrected time
                    if (adminTabBtn.classList.contains('active')) {
                        loadAdminData();
                    }
                })
                .catch(err => console.error("❌ Error synchronizing time:", err));
        }
        
        // Function to get server-corrected time
        function getCorrectedTime() {
            const now = new Date();
            return new Date(now.getTime() + serverTimeOffset);
        }
        
        // Store sync_info globally for timer updates
        let currentSyncInfo = null;
        
        // Update next sync countdown display with server time sync
        function updateNextSyncCountdown(sync_info) {
            const nextSyncEl = document.getElementById('adminNextSync');
            if (!nextSyncEl) return;
            
            // Store current sync info for continuous updates
            if (sync_info) {
                currentSyncInfo = sync_info;
            }
            
            // Use stored sync info if no new data provided
            const workingSyncInfo = sync_info || currentSyncInfo;
            
            if (!workingSyncInfo || !workingSyncInfo.auto_sync_enabled || !workingSyncInfo.last_sync_time) {
                nextSyncEl.textContent = '-';
                return;
            }
            
            try {
                // Use server-provided timestamp directly (should be timezone-aware)
                const lastSync = new Date(workingSyncInfo.last_sync_time);
                const syncIntervalMs = (workingSyncInfo.sync_interval_minutes || 5) * 60 * 1000;
                const nextSync = new Date(lastSync.getTime() + syncIntervalMs);
                
                // Use server-corrected time
                const now = getCorrectedTime();
                const timeToNext = nextSync - now;
                
                console.log('🕒 Countdown calc - Last:', lastSync, 'Next:', nextSync, 'Now (corrected):', now, 'Offset:', serverTimeOffset, 'Diff:', timeToNext);
                
                if (timeToNext <= 0) {
                    nextSyncEl.textContent = 'Any moment now';
                } else {
                    const minutes = Math.floor(timeToNext / 60000);
                    const seconds = Math.floor((timeToNext % 60000) / 1000);
                    
                    // Ensure we don't show negative values
                    if (minutes >= 0 && seconds >= 0) {
                        nextSyncEl.textContent = `${minutes}m ${seconds}s`;
                    } else {
                        nextSyncEl.textContent = 'Any moment now';
                    }
                }
            } catch (error) {
                console.warn('❌ Error updating countdown:', error);
                nextSyncEl.textContent = 'Calculating...';
            }
        }
        
        // Start sync countdown timer with periodic server sync
        function startSyncCountdownTimer(sync_info) {
            // Clear existing timer
            if (syncCountdownInterval) {
                clearInterval(syncCountdownInterval);
            }
            
            // Store sync info for persistent updates
            if (sync_info) {
                currentSyncInfo = sync_info;
            }
            
            // Only start if sync is enabled
            if (!currentSyncInfo || !currentSyncInfo.auto_sync_enabled) {
                console.log('⏹️ Countdown timer not started - sync not enabled');
                return;
            }
            
            console.log('▶️ Starting countdown timer with sync info:', currentSyncInfo);
            
            // Force immediate update
            updateNextSyncCountdown();
            
            // Update countdown every second using stored sync info
            syncCountdownInterval = setInterval(() => {
                updateNextSyncCountdown(); // No parameter - uses stored currentSyncInfo
            }, 1000);
            
            // Periodically re-sync with server time to prevent drift
            setTimeout(() => {
                if (adminTabBtn.classList.contains('active')) {
                    synchronizeWithServerTime();
                }
            }, 120000); // Every 2 minutes
        }
        
        // Stop sync countdown timer
        function stopSyncCountdownTimer() {
            if (syncCountdownInterval) {
                clearInterval(syncCountdownInterval);
                syncCountdownInterval = null;
            }
        }

        // Update action button counts
        function updateActionButtonCounts(data) {
            const pendingCount = data.last_sync_stats?.new_files_detected || 0;
            const cleanupCount = data.last_sync_stats?.deleted_files_detected || 0;

            const pendingEl = document.getElementById('pendingCount');
            const cleanupEl = document.getElementById('cleanupCount');

            if (pendingEl) {
                pendingEl.textContent = pendingCount;
                pendingEl.style.display = pendingCount > 0 ? 'block' : 'none';
            }

            if (cleanupEl) {
                cleanupEl.textContent = cleanupCount;
                cleanupEl.style.display = cleanupCount > 0 ? 'block' : 'none';
            }
        }

        // Update system status bars
        function updateSystemStatus(resourceStatus) {
            if (!resourceStatus) return;

            // Disk usage
            if (resourceStatus.disk) {
                const diskBar = document.getElementById('diskUsageBar');
                const diskText = document.getElementById('diskUsageText');
                if (diskBar && diskText) {
                    const usagePercent = resourceStatus.disk.usage_percent || 0;
                    diskBar.style.width = usagePercent + '%';
                    diskBar.className = 'status-progress ' + 
                        (usagePercent > 90 ? 'critical' : usagePercent > 80 ? 'warning' : '');
                    diskText.textContent = `${resourceStatus.disk.free_space_gb}GB free (${usagePercent.toFixed(1)}% used)`;
                }
            }

            // Memory usage
            if (resourceStatus.memory) {
                const memoryBar = document.getElementById('memoryUsageBar');
                const memoryText = document.getElementById('memoryUsageText');
                if (memoryBar && memoryText) {
                    const usagePercent = resourceStatus.memory.usage_percent || 0;
                    memoryBar.style.width = usagePercent + '%';
                    memoryBar.className = 'status-progress ' + 
                        (usagePercent > 90 ? 'critical' : usagePercent > 80 ? 'warning' : '');
                    memoryText.textContent = `${resourceStatus.memory.available_gb}GB available (${usagePercent.toFixed(1)}% used)`;
                }
            }

            // SharePoint connection status
            const sharepointStatusEl = document.getElementById('sharepointStatus');
            if (sharepointStatusEl) {
                sharepointStatusEl.textContent = 'Connected';
                sharepointStatusEl.className = 'status-value connection-connected';
            }

            // Background sync status
            const backgroundSyncEl = document.getElementById('backgroundSyncStatus');
            if (backgroundSyncEl && adminData.syncStatus) {
                const isRunning = adminData.syncStatus.sync_info?.background_sync_running;
                backgroundSyncEl.textContent = isRunning ? 'Running' : 'Stopped';
                backgroundSyncEl.className = 'status-value ' + (isRunning ? 'connection-connected' : 'connection-disconnected');
            }
        }

        // Update activity feed
        function updateAdminActivityFeed(data) {
            const activityFeed = document.getElementById('activityFeed');
            if (!activityFeed) return;

            const notifications = data.notifications || [];
            
            if (notifications.length === 0) {
                activityFeed.innerHTML = `
                    <div class="activity-item">
                        <span class="activity-icon">✅</span>
                        <span class="activity-text">No recent activity - system is running smoothly</span>
                    </div>
                `;
                return;
            }

            activityFeed.innerHTML = notifications.slice(0, 10).map(notification => {
                const icon = getNotificationIcon(notification.type);
                
                // Use server-corrected time for notification timestamps
                const notificationTime = new Date(notification.timestamp);
                const now = getCorrectedTime();
                const timeDiff = now - notificationTime;
                const minutes = Math.floor(timeDiff / 60000);
                
                let time;
                if (minutes < 1) {
                    time = 'Just now';
                } else if (minutes < 60) {
                    time = `${minutes} minute${minutes === 1 ? '' : 's'} ago`;
                } else {
                    const hours = Math.floor(minutes / 60);
                    time = `${hours} hour${hours === 1 ? '' : 's'} ago`;
                }
                
                return `
                    <div class="activity-item">
                        <span class="activity-icon">${icon}</span>
                        <div style="flex: 1;">
                            <span class="activity-text">${notification.message}</span>
                            <div style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.25rem;">${time}</div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Get notification icon
        function getNotificationIcon(type) {
            switch (type) {
                case 'sync_success': return '✅';
                case 'sync_disabled': return '🚫';
                case 'import_errors': return '⚠️';
                case 'deletion_approval_needed': return '🗑️';
                case 'orphaned_documents': return '🔍';
                case 'settings_updated': return '⚙️';
                default: return '📋';
            }
        }

        // Update settings panel
        function updateAdminSettings(data) {
            const settings = data.enhanced_sync_settings;
            if (!settings) return;

            // Update sync mode radio buttons
            const modeMapping = {
                'manual': !settings.auto_sync_enabled,
                'monitoring': settings.auto_sync_enabled && !settings.auto_import_enabled,
                'auto-import': settings.auto_import_enabled && !settings.auto_delete_enabled,
                'full-auto': settings.auto_import_enabled && settings.auto_delete_enabled
            };

            Object.entries(modeMapping).forEach(([mode, isActive]) => {
                const radio = document.getElementById('mode' + mode.charAt(0).toUpperCase() + mode.slice(1));
                if (radio) radio.checked = isActive;
            });

            // Update settings inputs
            const syncIntervalEl = document.getElementById('syncInterval');
            if (syncIntervalEl) syncIntervalEl.value = settings.sync_interval_minutes;

            const fileSizeLimitEl = document.getElementById('fileSizeLimit');
            if (fileSizeLimitEl) fileSizeLimitEl.value = settings.max_file_size_auto_import_mb;

            const dailyLimitEl = document.getElementById('dailyLimit');
            if (dailyLimitEl) dailyLimitEl.value = settings.max_files_per_sync;

            const notificationEmailEl = document.getElementById('notificationEmail');
            if (notificationEmailEl) notificationEmailEl.value = settings.auto_sync_notification_email || '';

            const errorThresholdEl = document.getElementById('errorThreshold');
            if (errorThresholdEl) errorThresholdEl.value = settings.auto_sync_error_threshold;

            const requireApprovalEl = document.getElementById('requireApproval');
            if (requireApprovalEl) requireApprovalEl.checked = settings.auto_sync_require_manual_approval;
        }

        // Setup admin event handlers
        function setupAdminEventHandlers() {
            // Admin tab button
            const adminTabBtn = document.getElementById('adminTabBtn');
            if (adminTabBtn) {
                adminTabBtn.addEventListener('click', toggleAdminDashboard);
            }

            // Sync mode selector
            const syncModeRadios = document.querySelectorAll('input[name="syncMode"]');
            syncModeRadios.forEach(radio => {
                radio.addEventListener('change', () => {
                    if (radio.checked) {
                        updateSyncModePreview(radio.value);
                    }
                });
            });

            // Save sync mode button
            const saveSyncModeBtn = document.getElementById('saveSyncMode');
            if (saveSyncModeBtn) {
                saveSyncModeBtn.addEventListener('click', saveSyncMode);
            }

            // Reset sync mode button
            const resetSyncModeBtn = document.getElementById('resetSyncMode');
            if (resetSyncModeBtn) {
                resetSyncModeBtn.addEventListener('click', resetSyncMode);
            }

            // Quick action buttons
            const syncNowBtn = document.getElementById('syncNowBtn');
            if (syncNowBtn) {
                syncNowBtn.addEventListener('click', () => performQuickAction('sync-now'));
            }

            const importPendingBtn = document.getElementById('importPendingBtn');
            if (importPendingBtn) {
                importPendingBtn.addEventListener('click', () => performQuickAction('import-pending'));
            }

            const cleanupBtn = document.getElementById('cleanupBtn');
            if (cleanupBtn) {
                cleanupBtn.addEventListener('click', () => performQuickAction('cleanup'));
            }

            const indexCleanupBtn = document.getElementById('indexCleanupBtn');
            if (indexCleanupBtn) {
                indexCleanupBtn.addEventListener('click', () => performQuickAction('index-cleanup'));
            }

            const resetSyncErrorsBtn = document.getElementById('resetSyncErrorsBtn');
            if (resetSyncErrorsBtn) {
                resetSyncErrorsBtn.addEventListener('click', () => performQuickAction('reset-sync-errors'));
            }

            // Settings buttons
            const saveSettingsBtn = document.getElementById('saveSettings');
            if (saveSettingsBtn) {
                saveSettingsBtn.addEventListener('click', saveSettings);
            }

            const resetSettingsBtn = document.getElementById('resetSettings');
            if (resetSettingsBtn) {
                resetSettingsBtn.addEventListener('click', resetSettings);
            }

            // Advanced settings toggle
            const advancedToggle = document.getElementById('advancedToggle');
            const advancedSettings = document.getElementById('advancedSettings');
            if (advancedToggle && advancedSettings) {
                advancedToggle.addEventListener('click', () => {
                    const isVisible = advancedSettings.style.display !== 'none';
                    advancedSettings.style.display = isVisible ? 'none' : 'block';
                    advancedToggle.textContent = isVisible ? 'Show Advanced' : 'Hide Advanced';
                });
            }

            // Refresh activity button
            const refreshActivityBtn = document.getElementById('refreshActivity');
            if (refreshActivityBtn) {
                refreshActivityBtn.addEventListener('click', loadAdminData);
            }

            // Test email button
            const testEmailBtn = document.getElementById('testEmail');
            if (testEmailBtn) {
                testEmailBtn.addEventListener('click', testEmailNotification);
            }
        }

        // Save sync mode
        async function saveSyncMode() {
            const selectedMode = document.querySelector('input[name="syncMode"]:checked');
            if (!selectedMode) return;

            const mode = selectedMode.value;
            const settings = getSyncModeSettings(mode);

            try {
                const response = await fetch(`${window.__RAG_BASE}/api/sync/settings/update?` + 
                    new URLSearchParams(settings), { 
                    method: 'POST',
                    credentials: 'include' 
                });

                if (response.ok) {
                    showAdminSuccess('Sync mode updated successfully!');
                    loadAdminData(); // Refresh data
                } else {
                    const error = await response.text();
                    showAdminError('Failed to update sync mode: ' + error);
                }
            } catch (error) {
                showAdminError('Error updating sync mode: ' + error.message);
            }
        }

        // Get settings for sync mode
        function getSyncModeSettings(mode) {
            switch (mode) {
                case 'manual':
                    return {
                        auto_sync_enabled: false,
                        auto_import_enabled: false,
                        auto_delete_enabled: false,
                        auto_sync_require_manual_approval: true
                    };
                case 'monitoring':
                    return {
                        auto_sync_enabled: true,
                        auto_import_enabled: false,
                        auto_delete_enabled: false,
                        auto_sync_require_manual_approval: true
                    };
                case 'auto-import':
                    return {
                        auto_sync_enabled: true,
                        auto_import_enabled: true,
                        auto_delete_enabled: false,
                        auto_sync_require_manual_approval: true
                    };
                case 'full-auto':
                    return {
                        auto_sync_enabled: true,
                        auto_import_enabled: true,
                        auto_delete_enabled: true,
                        auto_sync_require_manual_approval: false
                    };
                default:
                    return {};
            }
        }

        // Reset sync mode to safe defaults
        async function resetSyncMode() {
            if (!confirm('Reset sync mode to safe defaults (Smart Monitoring)?')) return;

            try {
                const response = await fetch(`${window.__RAG_BASE}/api/sync/settings/reset`, { 
                    method: 'POST',
                    credentials: 'include' 
                });

                if (response.ok) {
                    showAdminSuccess('Sync mode reset to safe defaults!');
                    loadAdminData(); // Refresh data
                } else {
                    const error = await response.text();
                    showAdminError('Failed to reset sync mode: ' + error);
                }
            } catch (error) {
                showAdminError('Error resetting sync mode: ' + error.message);
            }
        }

        // Perform quick actions
        async function performQuickAction(action) {
            const actionBtn = event.target.closest('.action-btn');
            let originalContent = null;
            let originalIcon = null;
            let originalText = null;
            
            if (actionBtn) {
                // Store original content
                const iconElement = actionBtn.querySelector('.action-icon');
                const textElement = actionBtn.querySelector('.action-text');
                if (iconElement) originalIcon = iconElement.innerHTML;
                if (textElement) originalText = textElement.innerHTML;
                
                // Set loading state
                actionBtn.style.opacity = '0.8';
                actionBtn.style.pointerEvents = 'none';
                actionBtn.classList.add('loading');
                
                // Update content to show loading
                if (iconElement) {
                    iconElement.innerHTML = '⏳';
                    iconElement.style.animation = 'pulse 1.5s infinite';
                }
                if (textElement) {
                    const actionName = action === 'sync-now' ? 'Syncing' : 
                                     action === 'import-pending' ? 'Importing' : 
                                     action === 'cleanup' ? 'Cleaning' : 'Processing';
                    textElement.innerHTML = `${actionName}...`;
                }
            }

            try {
                let endpoint = '';
                let method = 'POST';

                switch (action) {
                    case 'sync-now':
                        endpoint = '/api/sync/manual';
                        break;
                    case 'import-pending':
                        // This would trigger a manual sync focused on imports
                        endpoint = '/api/sync/manual';
                        break;
                    case 'cleanup':
                        // This triggers approval and execution of pending deletions
                        endpoint = '/api/sync/approve-deletions';
                        break;
                    case 'index-cleanup':
                        // This triggers cleanup of orphaned document references in the vector index
                        endpoint = '/api/documents/cleanup-index-references';
                        break;
                    case 'reset-sync-errors':
                        // This resets the background sync error state to re-enable disabled sync
                        endpoint = '/api/sync/reset-errors';
                        break;
                    default:
                        throw new Error('Unknown action: ' + action);
                }

                const response = await fetch(`${window.__RAG_BASE}${endpoint}`, { 
                    method,
                    credentials: 'include' 
                });

                if (response.ok) {
                    const result = await response.json();
                    showAdminSuccess(`${action} completed successfully!`);
                    loadAdminData(); // Refresh data
                } else {
                    const error = await response.text();
                    showAdminError(`Failed to ${action}: ${error}`);
                }
            } catch (error) {
                showAdminError(`Error performing ${action}: ${error.message}`);
            } finally {
                if (actionBtn) {
                    // Restore original state
                    actionBtn.style.opacity = '1';
                    actionBtn.style.pointerEvents = 'auto';
                    actionBtn.classList.remove('loading');
                    
                    // Restore original content
                    const iconElement = actionBtn.querySelector('.action-icon');
                    const textElement = actionBtn.querySelector('.action-text');
                    if (iconElement && originalIcon) {
                        iconElement.innerHTML = originalIcon;
                        iconElement.style.animation = '';
                    }
                    if (textElement && originalText) {
                        textElement.innerHTML = originalText;
                    }
                }
            }
        }

        // Save settings
        async function saveSettings() {
            const settings = {
                sync_interval_minutes: document.getElementById('syncInterval')?.value,
                max_file_size_auto_import_mb: document.getElementById('fileSizeLimit')?.value,
                max_files_per_sync: document.getElementById('dailyLimit')?.value,
                auto_sync_notification_email: document.getElementById('notificationEmail')?.value,
                auto_sync_error_threshold: document.getElementById('errorThreshold')?.value,
                auto_sync_require_manual_approval: document.getElementById('requireApproval')?.checked
            };

            // Filter out empty values
            Object.keys(settings).forEach(key => {
                if (settings[key] === '' || settings[key] === null || settings[key] === undefined) {
                    delete settings[key];
                }
            });

            try {
                const response = await fetch(`${window.__RAG_BASE}/api/sync/settings/update?` + 
                    new URLSearchParams(settings), { 
                    method: 'POST',
                    credentials: 'include' 
                });

                if (response.ok) {
                    showAdminSuccess('Settings saved successfully!');
                    loadAdminData(); // Refresh data
                } else {
                    const error = await response.text();
                    showAdminError('Failed to save settings: ' + error);
                }
            } catch (error) {
                showAdminError('Error saving settings: ' + error.message);
            }
        }

        // Reset settings
        async function resetSettings() {
            if (!confirm('Reset all settings to defaults?')) return;

            try {
                const response = await fetch(`${window.__RAG_BASE}/api/sync/settings/reset`, { 
                    method: 'POST',
                    credentials: 'include' 
                });

                if (response.ok) {
                    showAdminSuccess('Settings reset to defaults!');
                    loadAdminData(); // Refresh data
                } else {
                    const error = await response.text();
                    showAdminError('Failed to reset settings: ' + error);
                }
            } catch (error) {
                showAdminError('Error resetting settings: ' + error.message);
            }
        }

        // Test email notification
        async function testEmailNotification() {
            const email = document.getElementById('notificationEmail')?.value;
            if (!email) {
                showAdminError('Please enter an email address first');
                return;
            }

            // Save email settings first if needed
            try {
                await fetch(`${window.__RAG_BASE}/api/sync/settings/update?auto_sync_notification_email=${encodeURIComponent(email)}`, { 
                    method: 'POST',
                    credentials: 'include' 
                });
            } catch (error) {
                console.error('Failed to update email setting:', error);
            }

            // Send test email
            try {
                const response = await fetch(`${window.__RAG_BASE}/api/sync/test-email`, { 
                    method: 'POST',
                    credentials: 'include' 
                });

                if (response.ok) {
                    const result = await response.json();
                    showAdminSuccess(`Test email sent successfully to ${email}!`);
                } else {
                    const error = await response.json();
                    showAdminError(`Failed to send test email: ${error.detail || 'Unknown error'}`);
                }
            } catch (error) {
                showAdminError(`Error sending test email: ${error.message}`);
            }
        }

        // Show admin success message
        function showAdminSuccess(message) {
            // Create a temporary success message
            const successMsg = document.createElement('div');
            successMsg.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 9999;
                background-color: #10b981; color: white; padding: 1rem 1.5rem;
                border-radius: 0.5rem; box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                font-weight: 500; transition: opacity 0.3s ease;
            `;
            successMsg.textContent = message;
            document.body.appendChild(successMsg);

            setTimeout(() => {
                successMsg.style.opacity = '0';
                setTimeout(() => document.body.removeChild(successMsg), 300);
            }, 3000);
        }

        // Show admin error message
        function showAdminError(message) {
            // Create a temporary error message
            const errorMsg = document.createElement('div');
            errorMsg.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 9999;
                background-color: #ef4444; color: white; padding: 1rem 1.5rem;
                border-radius: 0.5rem; box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                font-weight: 500; transition: opacity 0.3s ease;
            `;
            errorMsg.textContent = message;
            document.body.appendChild(errorMsg);

            setTimeout(() => {
                errorMsg.style.opacity = '0';
                setTimeout(() => document.body.removeChild(errorMsg), 300);
            }, 5000);
        }

        // Start admin data polling
        function startAdminDataPolling() {
            // Refresh admin data every 30 seconds when dashboard is visible
            setInterval(() => {
                if (adminDashboardVisible) {
                    loadAdminData();
                }
            }, 30000);
        }

        // Call loadDocuments when the page loads and after successful login
        document.addEventListener('DOMContentLoaded', async () => {
            // <<< Add a small delay to allow session to potentially update >>>
            // await new Promise(resolve => setTimeout(resolve, 100)); // Delay might not be needed with new approach

            // Check for login success flag in URL
            const urlParams = new URLSearchParams(window.location.search);
            const loginSuccessFlag = urlParams.get('login_success') === 'true';
            
            // Clean the flag from the URL using history.replaceState
            if (loginSuccessFlag) {
                const newUrl = window.location.pathname + window.location.hash; // Keep hash if present
                // window.history.replaceState({}, document.title, newUrl); // <<< TEMPORARILY COMMENT OUT
                console.log("Login success flag found in URL. (URL cleaning disabled for test)");
            }

            // Check if user is already authenticated via API OR if flag is present
            let isAuthenticated = loginSuccessFlag;
            if (!isAuthenticated) { // Only check API if flag wasn't present
                try {
                    console.log("Checking authentication status via /user endpoint...");
                    const response = await fetch(`${window.__RAG_BASE}/user`, { credentials: 'include' });
                    const userData = await response.json();
                    console.log("Authentication status from /user:", userData);
                    if (userData.authenticated) {
                        isAuthenticated = true;
                    }
                } catch (error) {
                    console.error('Error checking authentication status:', error);
                    // Assume not authenticated if API check fails
                }
            }

            // Show content based on final authentication status
            if (isAuthenticated) {
                console.log("User is authenticated (via flag or API), showing main content");
                document.getElementById('loginScreen').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
                
                // Start periodic tasks
                startDocumentRefresh();
                startHealthChecks();
                
                // Initialize admin dashboard with slight delay to ensure session is ready
                setTimeout(() => {
                    initializeAdminDashboard();
                }, 1000);
            } else {
                console.log("User is not authenticated, showing login screen");
                document.getElementById('loginScreen').style.display = 'flex';
                document.getElementById('mainContent').style.display = 'none';
            }
        });

        // Function to create document item (simplified for SharePoint documents)
        function createDocumentItem(doc) {
            const div = document.createElement('div');
            div.className = 'document-item p-3 bg-white rounded-lg shadow-sm';
            
            div.innerHTML = `
                <div class="min-w-0">
                    <h3 class="font-medium text-gray-900 break-words">${doc.name}</h3>
                    <p class="text-sm text-gray-500 break-words">Type: ${doc.type || 'Document'}</p>
                    ${doc.source ? `<p class="text-sm text-gray-500">Source: ${doc.source}</p>` : ''}
                </div>
            `;

            return div;
        }

        // Chat functionality
        let currentResponse = '';
        let eventSource = null;

        function appendMessage(content, isUser = false, sourceNodes = [], followUpQuestions = [], isError = false, sourceMetadata = null, shouldScrollToBottom = true) {
            const messagesDiv = document.getElementById('chatMessages');
            
            // Hide welcome screen when first message is added
            hideWelcomeScreen();
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${isUser ? 'user-message' : 'assistant-message'} p-4 rounded-lg`;
            
            if (isUser) {
                messageDiv.textContent = content;
            } else {
                // Create main content area
                const contentArea = document.createElement('div');
                contentArea.className = 'main-content-area';
                contentArea.innerHTML = marked.parse(content);
                messageDiv.appendChild(contentArea);

                // Always add sources section (show placeholder if no sources)
                {
                    const sourcesSection = document.createElement('div');
                    sourcesSection.className = 'sources-section mt-4';
                    
                    // Track unique sources
                    const addedSources = new Set();
                    if (sourceNodes && sourceNodes.length > 0) {
                        sourceNodes.forEach((source) => {
                            if (source && (source.snippet || source.text)) {
                                const sourceKey = `${source.metadata?.file_name || ''}-${source.metadata?.sharepoint_id || source.snippet || source.text}`;
                                addedSources.add(sourceKey);
                            }
                        });
                    }
                    
                    // Always show sources section, even if no sources are available
                    const actualDisplayCount = addedSources.size;
                    
                    // Create sources header with metadata information
                    const sourcesHeader = document.createElement('div');
                    sourcesHeader.className = 'sources-header flex items-center justify-between cursor-pointer mb-2';
                    
                    // Build simple source count text
                    let sourceCountText = actualDisplayCount > 0 ? `Sources (${actualDisplayCount})` : 'Sources (0)';
                    
                    sourcesHeader.innerHTML = `
                        <div class="flex items-center gap-2">
                            <span class="source-arrow expanded">▶</span>
                            <span>${sourceCountText}</span>
                        </div>
                    `;
                    sourcesHeader.onclick = () => toggleSource(sourcesHeader);
                    sourcesSection.appendChild(sourcesHeader);

                    // Create sources content container
                    const sourcesContent = document.createElement('div');
                    sourcesContent.className = 'sources-content';

                    // Add each unique source with enhanced display
                    const displayedSources = new Set(); // Use separate set for tracking displayed sources
                    let actualSourcesDisplayed = 0;
                    
                    if (sourceNodes && sourceNodes.length > 0) {
                        sourceNodes.forEach((source, index) => {
                        if (source && (source.snippet || source.text)) {
                            // Use metadata for better deduplication
                            const sourceKey = `${source.metadata?.file_name || ''}-${source.metadata?.sharepoint_id || source.snippet || source.text}`;
                            if (!displayedSources.has(sourceKey)) {
                                displayedSources.add(sourceKey);
                                actualSourcesDisplayed++;
                                
                                const sourceDiv = document.createElement('div');
                                sourceDiv.className = 'source-citation mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors';
                                
                                // Extract snippet (prefer backend snippet, fallback to truncated text)
                                const displayText = source.snippet || (source.text && source.text.length > 200 ? source.text.substring(0, 200) + '…' : source.text);
                                
                                // Build download/view link using SharePoint ID or doc_id
                                const sharepointId = source.metadata?.sharepoint_id;
                                const docId = source.metadata?.doc_id;
                                const documentId = sharepointId || docId;
                                
                                const downloadLink = documentId ? 
                                    `<a href="${window.__RAG_BASE}/api/documents/${encodeURIComponent(documentId)}/view" target="_blank" class="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        View
                                    </a>` : '';
                                
                                // Build relevance indicator if score is available
                                const relevanceIndicator = source.score ? 
                                    `<span class="inline-flex items-center gap-1 text-xs font-medium text-gray-600">
                                        <svg class="w-3 h-3 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                        ${Math.round(source.score * 100)}% match
                                    </span>` : '';
                                
                                sourceDiv.innerHTML = `
                                    <div class="flex items-start justify-between mb-2">
                                        <div class="font-semibold text-gray-900 flex-1">${source.metadata?.file_name || 'Untitled Document'}</div>
                                        <div class="flex items-center gap-2 ml-2">
                                            ${relevanceIndicator}
                                            ${downloadLink}
                                        </div>
                                    </div>
                                    <div class="text-sm text-gray-700 mb-2 line-height-relaxed">${displayText}</div>
                                    <div class="flex items-center gap-4 text-xs text-gray-500">
                                        ${source.metadata?.created_datetime ? `<span>Uploaded: ${new Date(source.metadata.created_datetime).toLocaleDateString()}</span>` : ''}
                                        ${source.metadata?.last_modified_datetime ? `<span>Modified: ${new Date(source.metadata.last_modified_datetime).toLocaleDateString()}</span>` : ''}
                                        ${source.metadata?.parent_path ? `<span>Path: ${source.metadata.parent_path}</span>` : ''}
                                    </div>
                                `;
                                sourcesContent.appendChild(sourceDiv);
                            }
                        }
                        });
                    }

                    // If no sources were actually displayed, show a placeholder
                    if (actualSourcesDisplayed === 0) {
                        const noSourcesDiv = document.createElement('div');
                        noSourcesDiv.className = 'text-sm text-gray-500 p-4 text-center';
                        noSourcesDiv.textContent = 'No sources available for this response';
                        sourcesContent.appendChild(noSourcesDiv);
                    }

                    sourcesSection.appendChild(sourcesContent);
                    
                    // Sources section complete - no additional indicators needed
                    
                    // Always append the sources section
                    messageDiv.appendChild(sourcesSection);
                }

                // Add follow-up questions section if there are questions
                if (followUpQuestions && followUpQuestions.length > 0) {
                    const promptsSection = document.createElement('div');
                    promptsSection.className = 'prompts-section';
                    promptsSection.style.display = 'block';
                    
                    // Create prompts header
                    const promptsHeader = document.createElement('div');
                    promptsHeader.className = 'prompts-header';
                    promptsHeader.innerHTML = `
                        <span class="source-arrow expanded">▶</span>
                        <span>Recommended Questions</span>
                    `;
                    promptsHeader.onclick = () => toggleSource(promptsHeader);
                    promptsSection.appendChild(promptsHeader);

                    // Create prompts content
                    const promptsContent = document.createElement('div');
                    promptsContent.className = 'prompts-content';
                    promptsContent.style.display = 'block';

                    // Add each question
                    followUpQuestions.forEach(question => {
                        const promptDiv = document.createElement('div');
                        promptDiv.className = 'recommended-prompt';
                        promptDiv.textContent = question;
                        promptDiv.onclick = () => {
                            const currentTime = Date.now();
                            console.log('Recommended question clicked:', question, 'isRequestInProgress:', isRequestInProgress);
                            
                            // Debounce: prevent double-clicks within 500ms
                            if (lastClickedPrompt === question && currentTime - lastClickTime < 500) {
                                console.log('Debouncing duplicate click on same prompt');
                                return;
                            }
                            
                            // Only trigger if no request is in progress
                            if (!isRequestInProgress) {
                                lastClickedPrompt = question;
                                lastClickTime = currentTime;
                                
                                document.getElementById('queryInput').value = question;
                                // Trigger submit event that will handle scrolling
                                const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                                console.log('Dispatching submit event for recommended question');
                                try {
                                    document.getElementById('queryForm').dispatchEvent(submitEvent);
                                    console.log('Submit event dispatched successfully');
                                } catch (error) {
                                    console.error('Error dispatching submit event:', error);
                                    // Fallback: directly call the submit handler
                                    console.log('Using fallback: clicking send button directly');
                                    document.getElementById('sendButton').click();
                                }
                            } else {
                                console.log('Cannot start new query: request already in progress');
                                // Visual feedback when click is ignored
                                promptDiv.style.opacity = '0.5';
                                setTimeout(() => {
                                    promptDiv.style.opacity = '1';
                                }, 200);
                            }
                        };
                        promptsContent.appendChild(promptDiv);
                    });

                    promptsSection.appendChild(promptsContent);
                    messageDiv.appendChild(promptsSection);
                }
            }
            
            messagesDiv.appendChild(messageDiv);
            if (shouldScrollToBottom) {
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
        }

        // Function to toggle source content visibility
        window.toggleSource = function(header) {
            const content = header.nextElementSibling;
            const arrow = header.querySelector('.source-arrow');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                arrow.classList.add('expanded');
            } else {
                content.style.display = 'none';
                arrow.classList.remove('expanded');
            }
        };

        // Streaming message functions
        function createStreamingMessage() {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'chat-message assistant-message p-4 rounded-lg';

            // Create main content area with streaming indicator
            const contentArea = document.createElement('div');
            contentArea.className = 'main-content-area';
            contentArea.innerHTML = '<span class="streaming-cursor">▋</span>';
            messageDiv.appendChild(contentArea);

            messagesDiv.appendChild(messageDiv);

            return messageDiv;
        }

        function updateStreamingMessage(messageDiv, fullText) {
            const contentArea = messageDiv.querySelector('.main-content-area');
            // Parse markdown and add cursor
            contentArea.innerHTML = marked.parse(fullText) + '<span class="streaming-cursor">▋</span>';
            
            // No automatic scrolling during streaming - let user read in place
        }

        function finalizeStreamingMessage(messageDiv, fullText, sourceNodes, followUpQuestions, sourceMetadata = null) {
            const contentArea = messageDiv.querySelector('.main-content-area');
            // Remove cursor and finalize content
            contentArea.innerHTML = marked.parse(fullText);
            
            // Always add sources section (show placeholder if no sources)
            {
                const sourcesSection = document.createElement('div');
                sourcesSection.className = 'sources-section mt-4';
                
                // Track unique sources
                const addedSources = new Set();
                if (sourceNodes && sourceNodes.length > 0) {
                    sourceNodes.forEach((source) => {
                        if (source && (source.snippet || source.text)) {
                            const sourceKey = `${source.metadata?.file_name || ''}-${source.metadata?.sharepoint_id || source.snippet || source.text}`;
                            addedSources.add(sourceKey);
                        }
                    });
                }
                
                // Create sources header with metadata information
                const sourcesHeader = document.createElement('div');
                sourcesHeader.className = 'sources-header flex items-center justify-between cursor-pointer mb-2';
                
                // Build simple source count text
                let sourceCountText = addedSources.size > 0 ? `Sources (${addedSources.size})` : 'Sources (0)';
                
                sourcesHeader.innerHTML = `
                    <div class="flex items-center gap-2">
                        <span class="source-arrow expanded">▶</span>
                        <span>${sourceCountText}</span>
                    </div>
                `;
                sourcesHeader.onclick = () => toggleSource(sourcesHeader);
                sourcesSection.appendChild(sourcesHeader);

                // Create sources content container
                const sourcesContent = document.createElement('div');
                sourcesContent.className = 'sources-content';

                // Add each unique source
                const displayedSources = new Set(); // Use separate set for tracking displayed sources
                let actualSourcesDisplayed = 0;
                
                if (sourceNodes && sourceNodes.length > 0) {
                    sourceNodes.forEach((source, index) => {
                    if (source && (source.snippet || source.text)) {
                        // Use metadata for better deduplication
                        const sourceKey = `${source.metadata?.file_name || ''}-${source.metadata?.sharepoint_id || source.snippet || source.text}`;
                        if (!displayedSources.has(sourceKey)) {
                            displayedSources.add(sourceKey);
                            actualSourcesDisplayed++;
                            
                            const sourceDiv = document.createElement('div');
                            sourceDiv.className = 'source-citation mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors';
                            
                            // Extract snippet (prefer backend snippet, fallback to truncated text)
                            const displayText = source.snippet || (source.text && source.text.length > 200 ? source.text.substring(0, 200) + '…' : source.text);
                            
                            // Build download/view link using SharePoint ID or doc_id
                            const sharepointId = source.metadata?.sharepoint_id;
                            const docId = source.metadata?.doc_id;
                            const documentId = sharepointId || docId;
                            
                            const downloadLink = documentId ? 
                                `<a href="${window.__RAG_BASE}/api/documents/${encodeURIComponent(documentId)}/view" target="_blank" class="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    View
                                </a>` : '';
                            
                            // Build relevance indicator if score is available
                            const relevanceIndicator = source.score ? 
                                `<span class="inline-flex items-center gap-1 text-xs font-medium text-gray-600">
                                    <svg class="w-3 h-3 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    ${Math.round(source.score * 100)}% match
                                </span>` : '';
                            
                            sourceDiv.innerHTML = `
                                <div class="flex items-start justify-between mb-2">
                                    <div class="font-semibold text-gray-900 flex-1">${source.metadata?.file_name || 'Untitled Document'}</div>
                                    <div class="flex items-center gap-2 ml-2">
                                        ${relevanceIndicator}
                                        ${downloadLink}
                                    </div>
                                </div>
                                <div class="text-sm text-gray-700 mb-2 line-height-relaxed">${displayText}</div>
                                <div class="flex items-center gap-4 text-xs text-gray-500">
                                    ${source.metadata?.created_datetime ? `<span>Uploaded: ${new Date(source.metadata.created_datetime).toLocaleDateString()}</span>` : ''}
                                    ${source.metadata?.last_modified_datetime ? `<span>Modified: ${new Date(source.metadata.last_modified_datetime).toLocaleDateString()}</span>` : ''}
                                    ${source.metadata?.parent_path ? `<span>Path: ${source.metadata.parent_path}</span>` : ''}
                                </div>
                            `;
                            sourcesContent.appendChild(sourceDiv);
                        }
                    }
                    });
                }

                // If no sources were actually displayed, show a placeholder
                if (actualSourcesDisplayed === 0) {
                    const noSourcesDiv = document.createElement('div');
                    noSourcesDiv.className = 'text-sm text-gray-500 p-4 text-center';
                    noSourcesDiv.textContent = 'No sources available for this response';
                    sourcesContent.appendChild(noSourcesDiv);
                }

                sourcesSection.appendChild(sourcesContent);
                
                // Sources section complete - no additional indicators needed
                
                // Always append the sources section
                messageDiv.appendChild(sourcesSection);
            }

            // Add follow-up questions section if there are questions
            if (followUpQuestions && followUpQuestions.length > 0) {
                const promptsSection = document.createElement('div');
                promptsSection.className = 'prompts-section';
                promptsSection.style.display = 'block';
                
                // Create prompts header
                const promptsHeader = document.createElement('div');
                promptsHeader.className = 'prompts-header';
                promptsHeader.innerHTML = `
                    <span class="source-arrow expanded">▶</span>
                    <span>Recommended Questions</span>
                `;
                promptsHeader.onclick = () => toggleSource(promptsHeader);
                promptsSection.appendChild(promptsHeader);

                // Create prompts content
                const promptsContent = document.createElement('div');
                promptsContent.className = 'prompts-content';
                promptsContent.style.display = 'block';

                // Add each question
                followUpQuestions.forEach(question => {
                    const promptDiv = document.createElement('div');
                    promptDiv.className = 'recommended-prompt';
                    promptDiv.textContent = question;
                    promptDiv.onclick = () => {
                        const currentTime = Date.now();
                        console.log('Recommended question clicked:', question, 'isRequestInProgress:', isRequestInProgress);
                        
                        // Debounce: prevent double-clicks within 500ms
                        if (lastClickedPrompt === question && currentTime - lastClickTime < 500) {
                            console.log('Debouncing duplicate click on same prompt');
                            return;
                        }
                        
                        // Only trigger if no request is in progress
                        if (!isRequestInProgress) {
                            lastClickedPrompt = question;
                            lastClickTime = currentTime;
                            
                            document.getElementById('queryInput').value = question;
                            // Trigger submit event that will handle scrolling
                            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                            console.log('Dispatching submit event for recommended question');
                            try {
                                document.getElementById('queryForm').dispatchEvent(submitEvent);
                                console.log('Submit event dispatched successfully');
                            } catch (error) {
                                console.error('Error dispatching submit event:', error);
                                // Fallback: directly call the submit handler
                                console.log('Using fallback: clicking send button directly');
                                document.getElementById('sendButton').click();
                            }
                        } else {
                            console.log('Cannot start new query: request already in progress');
                            // Visual feedback when click is ignored
                            promptDiv.style.opacity = '0.5';
                            setTimeout(() => {
                                promptDiv.style.opacity = '1';
                            }, 200);
                        }
                    };
                    promptsContent.appendChild(promptDiv);
                });

                promptsSection.appendChild(promptsContent);
                messageDiv.appendChild(promptsSection);
            }
            
            // Optional: Only scroll to show sources if user hasn't manually scrolled
            // Let user decide when to scroll to see sources and questions
        }

        // Smart scrolling functions
        function scrollToShowNewMessage(messageDiv) {
            const messagesDiv = document.getElementById('chatMessages');
            // Scroll to show the start of the new message only once at the beginning
            messageDiv.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        // Track request state to prevent multiple simultaneous requests
        let isRequestInProgress = false;
        let lastClickedPrompt = null;
        let lastClickTime = 0;
        console.log('RAG Chat System v2.6 - Request state management initialized - ' + new Date().toISOString());
        
        // Add a simple test function to check backend connectivity
        window.testBackend = async function() {
            try {
                console.log('Testing backend connectivity...');
                const response = await fetch(`${window.__RAG_BASE}/health`, {
                    method: 'GET',
                    credentials: 'include'
                });
                console.log('Health check response:', response.status, response.ok);
                if (response.ok) {
                    const data = await response.json();
                    console.log('Health check data:', data);
                } else {
                    console.error('Health check failed:', response.status);
                }
            } catch (error) {
                console.error('Backend connectivity test failed:', error);
            }
        };

        // Add this function to reset the UI state
        function resetRequestState() {
            isRequestInProgress = false;
            const sendButton = document.getElementById('sendButton');
            const queryInput = document.getElementById('queryInput');
            if (sendButton) {
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
            }
            if (queryInput) {
                queryInput.disabled = false;
            }
            console.log('Request state reset - ready for new queries');
        }
        
        // Add visual indicator that new version is loaded
        setTimeout(() => {
            const indicator = document.createElement('div');
            indicator.style.position = 'fixed';
            indicator.style.bottom = '10px';
            indicator.style.right = '10px';
            indicator.style.backgroundColor = '#FFD100';
            indicator.style.color = 'black';
            indicator.style.padding = '5px 10px';
            indicator.style.borderRadius = '5px';
            indicator.style.fontSize = '12px';
            indicator.style.zIndex = '9999';
            indicator.textContent = 'v2.1 Loaded';
            document.body.appendChild(indicator);
            
            // Remove after 3 seconds
            setTimeout(() => indicator.remove(), 3000);
        }, 1000);
        
        // Global debugging function
        window.debugRAGState = function() {
            console.log('Debug RAG State:');
            console.log('isRequestInProgress:', isRequestInProgress);
            console.log('queryInput element:', document.getElementById('queryInput'));
            console.log('queryForm element:', document.getElementById('queryForm'));
            console.log('sendButton element:', document.getElementById('sendButton'));
        };

        // Handle query form submission with streaming
        document.getElementById('queryForm').addEventListener('submit', async (e) => {
            console.log('Form submit event triggered');
            e.preventDefault();
            const queryInput = document.getElementById('queryInput');
            const sendButton = document.getElementById('sendButton');
            const query = queryInput.value.trim();
            console.log('Query extracted:', query);
            if (!query) {
                console.log('Empty query, returning');
                return;
            }

            // Prevent multiple simultaneous requests
            if (isRequestInProgress) {
                console.log('Request already in progress, ignoring new request');
                return;
            }
            
            console.log('Starting new query:', query);

            // Set request state and update UI
            isRequestInProgress = true;
            const requestId = Date.now() + '-' + Math.random().toString(36).substring(7);
            console.log('DEBUG: Request ID:', requestId);
            sendButton.disabled = true;
            sendButton.textContent = 'Processing...';
            queryInput.disabled = true;

            appendMessage(query, true, [], [], false, null, false); // Display user query, don't scroll to bottom
            queryInput.value = ''; // Clear input

            // Scroll to show the new user query at the top of the visible area
            requestAnimationFrame(() => {
                const messagesContainer = document.getElementById('chatMessages');
                const userMessages = messagesContainer.querySelectorAll('.user-message');
                
                if (userMessages.length > 0) {
                    // Get the last user message (the one we just added)
                    const lastUserMessage = userMessages[userMessages.length - 1];
                    // Scroll to position it at the top of the visible area
                    messagesContainer.scrollTop = lastUserMessage.offsetTop;
                }
            });

            // Create a streaming message container
            const streamingMessageDiv = createStreamingMessage();

            // Start streaming response with timeout and AbortController
            try {
                // Create AbortController for request cancellation
                const controller = new AbortController();
                const timeoutMs = 5 * 60 * 1000; // 5 minutes client-side timeout
                
                // Set up timeout to abort the request
                const timeoutId = setTimeout(() => {
                    controller.abort();
                }, timeoutMs);
                
                console.log('DEBUG: Starting fetch for request:', requestId);
                console.log('DEBUG: Query being sent:', query);
                console.log('DEBUG: RAG_BASE:', window.__RAG_BASE);
                
                const streamUrl = `${window.__RAG_BASE}/query/stream?query=${encodeURIComponent(query)}`;
                console.log('DEBUG: Stream URL:', streamUrl);
                
                const response = await fetch(streamUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/plain',
                        'Cache-Control': 'no-cache',
                        'X-Request-ID': requestId
                    },
                    signal: controller.signal
                });
                console.log('DEBUG: Fetch response received for request:', requestId, 'Status:', response.status, 'Headers:', response.headers);

                if (!response.ok) {
                    let errorMessage = `HTTP error ${response.status}`;
                    if (response.status === 408) {
                        errorMessage = 'Request timed out. Please try a shorter or simpler query.';
                    } else if (response.status === 500) {
                        errorMessage = 'Server error. Please try again or contact support.';
                    } else if (response.status === 401) {
                        errorMessage = 'Authentication error. Please refresh the page and try again.';
                    }
                    throw new Error(errorMessage);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                let fullResponse = '';
                let sourceNodes = [];
                let followUpQuestions = [];
                let sourceMetadata = null;
                let streamCompleted = false;

                console.log('DEBUG: Starting stream read for request:', requestId);
                try {
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) {
                            console.log('DEBUG: Stream read completed (done=true) for request:', requestId);
                            break;
                        }

                        const chunk = decoder.decode(value, { stream: true });
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const dataContent = line.slice(6);
                                
                                // Handle SSE termination signal
                                if (dataContent.trim() === '[DONE]') {
                                    console.log('DEBUG: Received [DONE] signal for request:', requestId);
                                    streamCompleted = true;
                                    break; // Exit the loop
                                }
                                
                                try {
                                    const data = JSON.parse(dataContent);
                                    
                                    if (data.type === 'chunk') {
                                        fullResponse += data.content;
                                        updateStreamingMessage(streamingMessageDiv, fullResponse);
                                    } else if (data.type === 'complete') {
                                        sourceNodes = data.source_nodes || [];
                                        followUpQuestions = data.follow_up_questions || [];
                                        sourceMetadata = data.source_metadata || null;
                                        finalizeStreamingMessage(streamingMessageDiv, fullResponse, sourceNodes, followUpQuestions, sourceMetadata);
                                    } else if (data.type === 'error') {
                                        throw new Error(data.content);
                                    }
                                } catch (parseError) {
                                    // Check if this is a non-JSON data line (like [DONE])
                                    if (!dataContent.trim().startsWith('[') && !dataContent.trim().startsWith('{')) {
                                        console.log('DEBUG: Non-JSON data received:', dataContent);
                                    } else {
                                        console.warn('Failed to parse streaming data:', parseError, 'Data:', dataContent);
                                    }
                                }
                            }
                        }
                        
                        if (streamCompleted) {
                            break;
                        }
                    }
                } finally {
                    reader.releaseLock();
                    clearTimeout(timeoutId); // Clear timeout on successful completion
                    console.log('DEBUG: Stream cleanup completed for request:', requestId, 'Completed:', streamCompleted);
                    
                    // Reset request state and UI
                    resetRequestState();
                }

            } catch (error) {
                clearTimeout(timeoutId); // Clear timeout on error
                console.error('Streaming query error:', error);
                
                // Try fallback to non-streaming endpoint
                console.log('Attempting fallback to non-streaming endpoint...');
                try {
                    const fallbackResponse = await fetch(`${window.__RAG_BASE}/query`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Request-ID': requestId + '-fallback'
                        },
                        body: JSON.stringify({ query: query }),
                        credentials: 'include'
                    });
                    
                    if (fallbackResponse.ok) {
                        const fallbackData = await fallbackResponse.json();
                        console.log('Fallback response received:', fallbackData);
                        
                        // Clear the streaming message and show fallback response
                        if (streamingMessageDiv && streamingMessageDiv.parentNode) {
                            streamingMessageDiv.parentNode.removeChild(streamingMessageDiv);
                        }
                        
                        // Display the fallback response
                        appendMessage(
                            fallbackData.response || fallbackData.answer || 'Response received',
                            false,
                            fallbackData.sources || fallbackData.source_nodes || [],
                            fallbackData.follow_up_questions || fallbackData.followUpQuestions || [],
                            false
                        );
                        
                        resetRequestState();
                        return; // Exit successfully
                    } else {
                        console.error('Fallback endpoint also failed:', fallbackResponse.status);
                    }
                } catch (fallbackError) {
                    console.error('Fallback request failed:', fallbackError);
                }
                
                // Reset request state and UI on error
                resetRequestState();
                
                // Handle specific error types
                if (error.name === 'AbortError') {
                    // Add retry option for timeout errors
                    const retryMessage = `Request timed out after ${timeoutMs / 1000} seconds. Please try a simpler query or contact support if the issue persists.`;
                    const retryButton = document.createElement('button');
                    retryButton.className = 'btn-primary mt-2';
                    retryButton.textContent = 'Retry Query';
                    retryButton.onclick = () => {
                        // Re-trigger the query by setting the input value and submitting
                        if (!isRequestInProgress) {
                            document.getElementById('queryInput').value = query;
                            document.getElementById('queryForm').dispatchEvent(new Event('submit'));
                        }
                    };
                    
                    const errorDiv = document.createElement('div');
                    errorDiv.innerHTML = `<p>${retryMessage}</p>`;
                    errorDiv.appendChild(retryButton);
                    
                    appendMessage(errorDiv.innerHTML, false, [], [], true);
                } else {
                    appendMessage(`Error: ${error.message}`, false, [], [], true);
                }
            }
        });

        // New chat button
        document.getElementById('newChatBtn').addEventListener('click', () => {
            const chatMessages = document.getElementById('chatMessages');
            const welcomeScreen = document.getElementById('welcomeScreen');
            
            // Remove all messages except the welcome screen
            const children = Array.from(chatMessages.children);
            children.forEach(child => {
                if (child.id !== 'welcomeScreen') {
                    child.remove();
                }
            });
            
            // Show the welcome screen
            showWelcomeScreen();
        });

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', async () => {
            try {
                await fetch(`${window.__RAG_BASE}/logout`, { method: 'POST', credentials: 'include' });
                document.getElementById('loginScreen').style.display = 'flex';
                document.getElementById('mainContent').style.display = 'none';
            } catch (error) {
                console.error('Logout error:', error);
            }
        });

        // Add dark mode toggle functionality
        document.addEventListener('DOMContentLoaded', () => {
            const darkModeToggle = document.getElementById('darkModeToggle');
            const sunIcon = darkModeToggle.querySelector('.sun-icon');
            const moonIcon = darkModeToggle.querySelector('.moon-icon');

            // Function to update welcome logo based on theme
            function updateWelcomeLogo(isDark) {
                const welcomeLogo = document.getElementById('welcomeLogo');
                if (welcomeLogo) {
                    if (isDark) {
                        welcomeLogo.src = '/static/ddb-logo-80x80.png';
                    } else {
                        welcomeLogo.src = '/static/ddb-logo-login.png';
                    }
                }
            }

            // Check for saved theme preference, default to dark mode
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                document.documentElement.removeAttribute('data-theme');
                sunIcon.classList.add('hidden');
                moonIcon.classList.remove('hidden');
                updateWelcomeLogo(false);
            } else {
                // Default to dark mode
                document.documentElement.setAttribute('data-theme', 'dark');
                sunIcon.classList.remove('hidden');
                moonIcon.classList.add('hidden');
                updateWelcomeLogo(true);
                if (!savedTheme) {
                    localStorage.setItem('theme', 'dark');
                }
            }

            darkModeToggle.addEventListener('click', () => {
                const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
                
                if (isDark) {
                    document.documentElement.removeAttribute('data-theme');
                    localStorage.setItem('theme', 'light');
                    sunIcon.classList.add('hidden');
                    moonIcon.classList.remove('hidden');
                    updateWelcomeLogo(false);
                } else {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    localStorage.setItem('theme', 'dark');
                    sunIcon.classList.remove('hidden');
                    moonIcon.classList.add('hidden');
                    updateWelcomeLogo(true);
                }
            });
        });

        // Function to refresh documents periodically
        function startDocumentRefresh() {
            // Initial load
            loadDocuments();
            
            // Refresh every 30 seconds
            setInterval(loadDocuments, 30000);
        }

        // Welcome Screen Functions
        function showWelcomeScreen() {
            const welcomeScreen = document.getElementById('welcomeScreen');
            if (welcomeScreen) {
                welcomeScreen.style.display = 'flex';
            }
        }

        function hideWelcomeScreen() {
            const welcomeScreen = document.getElementById('welcomeScreen');
            if (welcomeScreen) {
                welcomeScreen.style.display = 'none';
            }
        }

        function checkWelcomeScreenVisibility() {
            const chatMessages = document.getElementById('chatMessages');
            const welcomeScreen = document.getElementById('welcomeScreen');
            
            if (chatMessages && welcomeScreen) {
                // Count actual chat messages (exclude the welcome screen)
                const children = Array.from(chatMessages.children);
                const messageCount = children.filter(child => child.id !== 'welcomeScreen').length;
                
                if (messageCount === 0) {
                    showWelcomeScreen();
                } else {
                    hideWelcomeScreen();
                }
            }
        }

        // Initialize welcome screen functionality
        document.addEventListener('DOMContentLoaded', () => {
            // Add click handlers to question cards
            const questionCards = document.querySelectorAll('.question-card');
            questionCards.forEach(card => {
                card.addEventListener('click', () => {
                    const questionText = card.querySelector('.question-text').textContent;
                    const queryInput = document.getElementById('queryInput');
                    const queryForm = document.getElementById('queryForm');
                    
                    // Populate the input field
                    queryInput.value = questionText;
                    
                    // Submit the form
                    const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                    queryForm.dispatchEvent(submitEvent);
                });
            });

            // Initial check for welcome screen visibility
            setTimeout(checkWelcomeScreenVisibility, 100);
        });
    </script>
</body>
</html> 