#!/usr/bin/env python3
"""
SharePoint Drive Discovery Utility

This script helps discover the correct SharePoint drive ID and validates
access to the target folder for the DDBrain RAG system.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add current directory to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from sharepoint_client import SharePointClient
from config import settings

async def discover_drives():
    """Discover all accessible SharePoint drives and find the target folder."""
    
    print("🔍 SharePoint Drive Discovery")
    print("=" * 50)
    
    # Initialize SharePoint client
    client = SharePointClient()
    
    try:
        # Get a valid token for API calls
        print("\n🔐 Acquiring access token...")
        from pathlib import Path
        
        token = await client.get_cached_token_with_refresh(
            cache_dir=Path("storage/data/token_caches")
        )
        
        if not token:
            print("❌ Failed to acquire access token. Please ensure you're logged in to SharePoint.")
            return None
            
        print("✅ Access token acquired successfully")
        
        # Step 1: List all sites
        print("\n1. Discovering SharePoint sites...")
        sites = await client.list_sites(token)
        
        if not sites:
            print("❌ No SharePoint sites found or access denied")
            return None
            
        print(f"✅ Found {len(sites)} accessible site(s):")
        for i, site in enumerate(sites):
            print(f"   {i+1}. {site.get('displayName', 'Unknown')} - ID: {site.get('id', 'Unknown')}")
        
        # Step 2: Check each site for drives
        target_folder = settings.SYNC_TARGET_FOLDER_PATH or "DDB Group Repository"
        print(f"\n2. Searching for folder: '{target_folder}'")
        
        valid_drives = []
        
        for site in sites:
            site_id = site.get('id')
            site_name = site.get('displayName', 'Unknown')
            
            if not site_id:
                continue
                
            print(f"\n📁 Checking site: {site_name}")
            
            try:
                # Get drives for this site
                drives = await client.list_drives(site_id, token)
                
                if not drives:
                    print(f"   ⚠️  No drives found in site {site_name}")
                    continue
                
                print(f"   📂 Found {len(drives)} drive(s):")
                
                for drive in drives:
                    drive_id = drive.get('id')
                    drive_name = drive.get('name', 'Unknown')
                    drive_type = drive.get('driveType', 'Unknown')
                    
                    print(f"      - {drive_name} ({drive_type}) - ID: {drive_id}")
                    
                    if not drive_id:
                        continue
                    
                    # Check if target folder exists in this drive
                    try:
                        print(f"         🔍 Checking for folder '{target_folder}'...")
                        files = await client.list_files_from_drive(drive_id, target_folder, token)
                        
                        if files is not None:
                            print(f"         ✅ Found '{target_folder}' with {len(files)} items!")
                            valid_drives.append({
                                'site_name': site_name,
                                'site_id': site_id,
                                'drive_name': drive_name,
                                'drive_id': drive_id,
                                'drive_type': drive_type,
                                'target_folder': target_folder,
                                'item_count': len(files)
                            })
                        else:
                            print(f"         ❌ Folder '{target_folder}' not found")
                            
                    except Exception as e:
                        print(f"         ❌ Error accessing folder: {str(e)}")
                        
            except Exception as e:
                print(f"   ❌ Error accessing drives in site {site_name}: {str(e)}")
        
        # Step 3: Report results
        print(f"\n3. Discovery Results")
        print("=" * 30)
        
        if not valid_drives:
            print(f"❌ No drives found containing folder '{target_folder}'")
            
            # Suggest possible solutions
            print("\n💡 Suggestions:")
            print("   1. Check if the folder name is correct in .env SYNC_TARGET_FOLDER_PATH")
            print("   2. Verify SharePoint permissions for the service account")
            print("   3. Ensure the folder exists in SharePoint")
            
            return None
        
        print(f"✅ Found {len(valid_drives)} valid drive(s) containing '{target_folder}':")
        
        for i, drive in enumerate(valid_drives):
            print(f"\n   {i+1}. Site: {drive['site_name']}")
            print(f"      Drive: {drive['drive_name']} ({drive['drive_type']})")
            print(f"      Drive ID: {drive['drive_id']}")
            print(f"      Items in folder: {drive['item_count']}")
        
        # Step 4: Recommend the best drive
        if len(valid_drives) == 1:
            recommended = valid_drives[0]
            print(f"\n🎯 Recommended Drive ID: {recommended['drive_id']}")
        else:
            # If multiple drives, prefer business drives over personal
            business_drives = [d for d in valid_drives if d['drive_type'] in ['business', 'documentLibrary']]
            
            if business_drives:
                recommended = business_drives[0]
                print(f"\n🎯 Recommended Drive ID (business): {recommended['drive_id']}")
            else:
                recommended = valid_drives[0]
                print(f"\n🎯 Recommended Drive ID (first found): {recommended['drive_id']}")
        
        print(f"\n📝 Add this to your .env file:")
        print(f"SYNC_TARGET_DRIVE_ID={recommended['drive_id']}")
        
        return recommended
        
    except Exception as e:
        print(f"❌ Error during drive discovery: {str(e)}")
        return None

async def test_drive_access(drive_id: str, folder_path: str = None):
    """Test access to a specific drive and folder."""
    
    folder_path = folder_path or settings.SYNC_TARGET_FOLDER_PATH or "DDB Group Repository"
    
    print(f"\n🧪 Testing Drive Access")
    print("=" * 30)
    print(f"Drive ID: {drive_id}")
    print(f"Folder: {folder_path}")
    
    client = SharePointClient()
    
    try:
        # Get a valid token for API calls
        from pathlib import Path
        
        token = await client.get_cached_token_with_refresh(
            cache_dir=Path("storage/data/token_caches")
        )
        
        if not token:
            print("❌ Failed to acquire access token. Please ensure you're logged in to SharePoint.")
            return False
        
        # Test folder access
        files = await client.list_files_from_drive(drive_id, folder_path, token)
        
        if files is not None:
            print(f"✅ Successfully accessed folder '{folder_path}'")
            print(f"📊 Found {len(files)} items in folder")
            
            # Show first few items
            if files:
                print("\n📄 Sample items:")
                for i, file in enumerate(files[:5]):
                    name = file.get('name', 'Unknown')
                    size = file.get('size', 0)
                    print(f"   {i+1}. {name} ({size} bytes)")
                
                if len(files) > 5:
                    print(f"   ... and {len(files) - 5} more")
            
            return True
        else:
            print(f"❌ Failed to access folder '{folder_path}'")
            return False
            
    except Exception as e:
        print(f"❌ Error testing drive access: {str(e)}")
        return False

async def main():
    """Main function to run drive discovery."""
    
    if len(sys.argv) > 1:
        # Test mode - test specific drive ID
        drive_id = sys.argv[1]
        folder_path = sys.argv[2] if len(sys.argv) > 2 else None
        await test_drive_access(drive_id, folder_path)
    else:
        # Discovery mode
        await discover_drives()

if __name__ == "__main__":
    asyncio.run(main())