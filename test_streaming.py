#!/usr/bin/env python3

"""
Simple test to verify streaming response works
"""

import asyncio
import json
from fastapi import Fast<PERSON><PERSON>, Response
from fastapi.responses import StreamingResponse

app = FastAPI()

@app.get("/test-stream")
async def test_stream():
    """Test streaming response with simple chunks."""
    
    def emergency_simple_stream():
        try:
            # Send immediate response chunks
            yield f"data: {json.dumps({'type': 'status', 'content': 'Processing your query...'})}\n\n"
            
            # Simple response
            simple_response = "This is a test streaming response to verify the mechanism works properly."
            
            # Break response into chunks
            words = simple_response.split(' ')
            for i in range(0, len(words), 3):
                chunk_words = words[i:i+3]
                chunk_text = ' '.join(chunk_words)
                
                chunk_data = {
                    'type': 'chunk',
                    'content': chunk_text + ' '
                }
                yield f"data: {json.dumps(chunk_data)}\n\n"
                
            # Send completion
            yield f"data: {json.dumps({'type': 'complete', 'sources': [], 'followUpQuestions': []})}\n\n"
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            print(f"Error: {e}")
            yield f"data: {json.dumps({'type': 'error', 'content': 'Test failed'})}\n\n"
            yield "data: [DONE]\n\n"
    
    return StreamingResponse(
        emergency_simple_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="localhost", port=8083)