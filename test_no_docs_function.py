#!/usr/bin/env python3
"""
Direct test of the handle_no_documents_response function
"""
import sys
import os
import logging

# Add the current directory to the path to import from app.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the function we want to test
from app import handle_no_documents_response
from config import Settings

def test_handle_no_documents_response():
    """Test the handle_no_documents_response function directly"""
    print("🧪 Testing handle_no_documents_response function")
    print("=" * 50)
    
    # Create a logger for testing
    logger = logging.getLogger("test")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    logger.addHandler(handler)
    
    # Test with a sample query
    test_query = "What is the company's vacation policy?"
    
    # Call the function
    response = handle_no_documents_response(test_query, logger)
    
    # Verify the response structure
    print("✅ Function executed successfully")
    print(f"Response structure: {list(response.keys())}")
    
    # Check required fields
    required_fields = ["message", "query", "sources", "follow_up_questions", "source_metadata", "no_documents_found"]
    for field in required_fields:
        if field in response:
            print(f"✅ Has required field: {field}")
        else:
            print(f"❌ Missing required field: {field}")
    
    # Check the message content
    print(f"\n📄 Message content:")
    print(f"'{response['message']}'")
    
    # Check if it's properly formatted
    if "What is the company's vacation policy?" in response['message'] or '"What is the company\'s vacation policy?"' in response['message']:
        print("✅ Message correctly includes the query")
    else:
        print("❌ Message does not include the query")
    
    # Check no_documents_found flag
    if response.get('no_documents_found') is True:
        print("✅ no_documents_found flag is correctly set to True")
    else:
        print("❌ no_documents_found flag is not set correctly")
    
    # Check source metadata
    source_metadata = response.get('source_metadata', {})
    if (source_metadata.get('total_sources') == 0 and 
        source_metadata.get('relevant_sources') == 0 and 
        source_metadata.get('displayed_sources') == 0):
        print("✅ Source metadata is correctly set to zero")
    else:
        print("❌ Source metadata is not correctly set")
        print(f"Source metadata: {source_metadata}")
    
    # Test with empty query
    print("\n--- Testing with empty query ---")
    empty_response = handle_no_documents_response("", logger)
    print(f"Empty query message: '{empty_response['message']}'")
    
    print("\n" + "=" * 50)
    print("🏁 Direct function testing completed!")
    
    return response

def test_config_setting():
    """Test that the configuration setting is properly loaded"""
    print("\n🔧 Testing configuration setting")
    print("=" * 30)
    
    try:
        settings = Settings()
        print(f"✅ NO_DOCUMENTS_MESSAGE setting loaded:")
        print(f"'{settings.NO_DOCUMENTS_MESSAGE}'")
        
        # Check if it's the expected default
        if "don't have any relevant documents" in settings.NO_DOCUMENTS_MESSAGE:
            print("✅ Setting contains expected content")
        else:
            print("❌ Setting does not contain expected content")
            
    except Exception as e:
        print(f"❌ Error loading settings: {e}")

if __name__ == "__main__":
    test_handle_no_documents_response()
    test_config_setting()