{"timestamp": "2025-07-04T01:12:33.708855", "environment": "development", "test_results": {"drive_discovery": {"status": "FAILED", "error": "No access token available", "recommendation": "Ensure user is logged in to SharePoint via the web interface"}, "token_access": {"status": "FAILED", "error": "No token available"}, "webhook_configuration": {"status": "WARNING", "webhook_url": "http://localhost:8082/api/sync/webhook-notification", "supported": true, "note": "HTTP webhooks not supported by Microsoft Graph", "recommendation": "Use HTTPS URL or deploy to production"}, "background_sync": {"status": "FAILED", "error": "Status endpoint returned 401"}, "notification_delivery": {"status": "WARNING", "email_status": "CONFIGURED", "sidebar_status": "FAILED", "email_enabled": true}, "file_change_detection": {"status": "FAILED", "error": "No access token"}}, "summary": {"total_tests": 6, "passed": 0, "failed": 4, "warnings": 2, "errors": 0}}