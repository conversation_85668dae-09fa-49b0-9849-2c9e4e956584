# DDB RAG Application - User Manual

## Table of Contents
1. [Overview](#overview)
2. [Getting Started](#getting-started)
3. [User Interface Guide](#user-interface-guide)
4. [Features and Functionality](#features-and-functionality)
5. [SharePoint Integration](#sharepoint-integration)
6. [Document Management](#document-management)
7. [Search and Query](#search-and-query)
8. [Administration](#administration)
9. [Troubleshooting](#troubleshooting)
10. [Best Practices](#best-practices)

---

## Overview

The DDB RAG (Retrieval-Augmented Generation) Application is an intelligent document search and query system that integrates with SharePoint to provide AI-powered responses based on your organization's documents. The application uses advanced language models to understand your questions and retrieve relevant information from your document collection.

### Key Features
- **AI-Powered Search**: Ask questions in natural language and get intelligent responses
- **SharePoint Integration**: Seamlessly connect to and import documents from SharePoint
- **Multi-Format Support**: Works with PDF, Word, Excel, PowerPoint, images, and text files
- **Real-Time Sync**: Automatically syncs with <PERSON><PERSON><PERSON><PERSON> changes
- **Responsive Design**: Works on desktop and mobile devices
- **Dark Mode**: Toggle between light and dark themes
- **Admin Controls**: Comprehensive administration features for document management

---

## Getting Started

### Accessing the Application

1. **Open your web browser** and navigate to the application URL (typically `http://localhost:8082` for local installations)
2. **Login Screen**: You'll be presented with a login screen
3. **Authentication Options**:
   - **Basic Authentication**: Use your username and password
   - **Microsoft OAuth**: Click "Sign in with Microsoft" for SharePoint integration

### First-Time Setup

1. **Login** using your credentials
2. **Check Connection Status**: The application will display connection status indicators
3. **Import Documents**: If you're an admin, you can import documents from SharePoint or upload files directly

---

## User Interface Guide

### Main Interface Layout

The application features a clean, responsive design with the following components:

#### Desktop Layout
- **Sidebar (Left)**: Navigation menu with options for:
  - New Chat
  - Documents
  - SharePoint Sites (if enabled)
  - Admin functions
- **Main Area**: Chat interface for asking questions
- **Header**: Application title, user info, and settings

#### Mobile Layout
- **Burger Menu**: Collapsible sidebar accessible via hamburger icon
- **Full-Screen Chat**: Optimized chat interface for mobile devices

### Navigation Menu

#### For All Users
- **🏠 Home**: Return to main chat interface
- **📄 Documents**: View and manage uploaded documents
- **🔍 New Chat**: Start a fresh conversation

#### For Admin Users
- **🌐 SharePoint Sites**: Browse and import from SharePoint
- **⚙️ Admin Panel**: Access administrative functions
- **📊 Sync Status**: Monitor automatic synchronization

### Dark Mode Toggle

- **Location**: Top-right corner of the interface
- **Persistence**: Your theme preference is saved automatically
- **Accessibility**: Optimized for both light and dark viewing preferences

---

## Features and Functionality

### Chat Interface

#### Asking Questions
1. **Type your question** in the chat input field at the bottom
2. **Press Enter** or click the send button
3. **Wait for response**: The AI will process your question and provide an answer
4. **Follow-up questions**: Continue the conversation with related queries

#### Question Types
- **Direct Questions**: "What is the company's vacation policy?"
- **Comparative Questions**: "How does our Q1 performance compare to last year?"
- **Analytical Questions**: "What are the key risks mentioned in the project plan?"
- **Summary Requests**: "Summarize the main points of the quarterly report"

#### Response Features
- **Source Citations**: Responses include references to source documents
- **Contextual Answers**: AI provides relevant context and details
- **Follow-up Suggestions**: System may suggest related questions
- **Formatting**: Responses are formatted with proper headings, lists, and emphasis

### Real-Time Features

#### Health Monitoring
- **Connection Status**: Automatic health checks every 60 seconds
- **Auto-Reconnect**: System automatically attempts to reconnect if connection is lost
- **Status Indicators**: Visual indicators show system health

#### Document Sync
- **Real-Time Updates**: Document list refreshes every 30 seconds
- **Sync Status**: Monitor synchronization progress
- **Change Notifications**: Get notified when documents are added or updated

---

## SharePoint Integration

### Prerequisites
- Microsoft 365 account with SharePoint access
- Proper permissions to access SharePoint sites and documents
- Admin configuration of SharePoint connection

### Browsing SharePoint

#### Sites
1. **Navigate to SharePoint Sites**: Click on "SharePoint Sites" in the sidebar
2. **Site List**: View all accessible SharePoint sites
3. **Site Selection**: Click on a site to explore its document libraries

#### Document Libraries
1. **Library Access**: Browse through different document libraries
2. **Folder Navigation**: Navigate through folder structures
3. **File Preview**: View file details and metadata

### Document Import Process

#### Manual Import (Admin Only)
1. **Browse to desired files** in SharePoint
2. **Select files** for import
3. **Click "Import"** to add files to the system
4. **Monitor progress** through the import status

#### Automatic Sync
- **Background Process**: System automatically checks for new documents
- **Configurable Intervals**: Sync frequency can be adjusted by administrators
- **Smart Detection**: Only imports new or modified documents

---

## Document Management

### Supported File Types

#### Documents
- **PDF**: Portable Document Format files
- **Word**: .docx files (Microsoft Word)
- **PowerPoint**: .pptx files (Microsoft PowerPoint)
- **Excel**: .xlsx files (Microsoft Excel)
- **Text**: .txt and .md files

#### Images (with OCR)
- **JPEG/JPG**: Joint Photographic Experts Group
- **PNG**: Portable Network Graphics
- **GIF**: Graphics Interchange Format
- **BMP**: Bitmap Image File

### Document Processing

#### Upload Process
1. **File Upload**: Documents are uploaded to the system
2. **Format Detection**: System identifies file type and format
3. **Content Extraction**: Text and metadata are extracted
4. **OCR Processing**: Images are processed for text recognition
5. **Indexing**: Content is indexed for search and retrieval
6. **Verification**: Import success is verified

#### Metadata Handling
- **File Properties**: Original file properties are preserved
- **SharePoint Metadata**: SharePoint-specific metadata is retained
- **Creation/Modification Dates**: Timestamps are tracked
- **Author Information**: Document authors are recorded where available

### Document List View

#### Information Displayed
- **Document Name**: Original filename
- **Type**: File format indicator
- **Size**: File size in human-readable format
- **Upload Date**: When document was added to system
- **Source**: Origin (SharePoint, direct upload, etc.)
- **Status**: Processing status indicator

#### Actions Available
- **View Details**: See comprehensive document information
- **Re-index**: Refresh document indexing (admin only)
- **Delete**: Remove document from system (admin only)

---

## Search and Query

### Query Processing

#### How It Works
1. **Question Analysis**: AI analyzes your question to understand intent
2. **Document Retrieval**: System searches through indexed documents
3. **Relevance Ranking**: Results are ranked by relevance using advanced algorithms
4. **Context Assembly**: Relevant document sections are compiled
5. **Response Generation**: AI generates a comprehensive answer
6. **Source Attribution**: Response includes citations to source documents

#### Query Optimization
- **Similarity Threshold**: Filters out irrelevant results
- **Top-K Retrieval**: Returns the most relevant document sections
- **Reranking**: Uses Cohere reranking for improved accuracy
- **Context Limiting**: Manages response length and relevance

### Search Tips

#### Effective Questioning
- **Be Specific**: "What are the Q1 sales figures for the North region?" vs. "Sales info"
- **Use Context**: "According to the project plan, what are the main milestones?"
- **Ask for Comparisons**: "How does this year's budget compare to last year's?"
- **Request Analysis**: "What are the key risks identified in the risk assessment?"

#### Advanced Queries
- **Multi-Document**: "Compare the recommendations from all quarterly reports"
- **Temporal**: "What changes were made to the policy since last year?"
- **Categorical**: "Show me all documents related to HR policies"
- **Analytical**: "What trends can you identify in the sales data?"

---

## Administration

### Admin Dashboard

#### Access Requirements
- Admin role assignment (configured via ADMIN_EMAILS environment variable)
- Proper authentication credentials
- Administrative privileges in SharePoint (if applicable)

#### Admin Features
- **Document Management**: Add, remove, and manage documents
- **SharePoint Integration**: Configure and manage SharePoint connections
- **Sync Management**: Control automatic synchronization
- **User Management**: Monitor user access and activity
- **System Configuration**: Adjust system settings and parameters

### Document Administration

#### Import Management
- **Bulk Import**: Import multiple documents simultaneously
- **Selective Import**: Choose specific documents for import
- **Import History**: Track import activities and results
- **Error Handling**: Manage failed imports and retry operations

#### Sync Configuration
- **Automatic Sync**: Enable/disable automatic synchronization
- **Sync Intervals**: Configure how often sync occurs
- **Sync Scope**: Define which SharePoint locations to sync
- **Deletion Handling**: Manage how deleted documents are handled

### System Monitoring

#### Health Checks
- **System Status**: Monitor overall system health
- **Connection Status**: Check SharePoint connectivity
- **Processing Queue**: Monitor document processing status
- **Error Logs**: Access system error logs and diagnostics

#### Performance Metrics
- **Response Times**: Monitor query response performance
- **Document Count**: Track number of indexed documents
- **Storage Usage**: Monitor storage consumption
- **API Usage**: Track API call usage and limits

---

## Troubleshooting

### Common Issues

#### Login Problems
**Issue**: Cannot log in to the application
**Solutions**:
- Verify username and password are correct
- Check if Microsoft OAuth is configured properly
- Clear browser cookies and cache
- Contact administrator to verify account access

#### Document Import Issues
**Issue**: Documents fail to import from SharePoint
**Solutions**:
- Verify SharePoint permissions
- Check file format compatibility
- Ensure file size is within limits (default 50MB)
- Check network connectivity to SharePoint

#### Search Not Working
**Issue**: Queries return no results or poor results
**Solutions**:
- Verify documents have been imported and indexed
- Try rephrasing your question
- Check if documents contain the information you're looking for
- Contact administrator to verify system configuration

#### Performance Issues
**Issue**: Application is slow or unresponsive
**Solutions**:
- Check internet connection
- Refresh the browser page
- Clear browser cache
- Contact administrator if issues persist

### Error Messages

#### "Authentication Failed"
- **Cause**: Invalid credentials or expired session
- **Solution**: Re-login with correct credentials

#### "Document Processing Failed"
- **Cause**: Unsupported file format or corrupted file
- **Solution**: Verify file format and try re-uploading

#### "SharePoint Connection Error"
- **Cause**: Network issues or SharePoint configuration problems
- **Solution**: Check network connectivity and SharePoint settings

#### "Query Timeout"
- **Cause**: Complex query taking too long to process
- **Solution**: Simplify query or try again later

### Getting Help

#### Contact Information
- **Technical Support**: Contact your system administrator
- **Documentation**: Refer to this user manual
- **System Logs**: Administrators can check application logs for detailed error information

#### Reporting Issues
When reporting issues, include:
- Error message (if any)
- Steps to reproduce the problem
- Browser type and version
- Screenshot (if applicable)
- Time when issue occurred

---

## Best Practices

### Effective Usage

#### Query Strategies
- **Start Broad, Then Narrow**: Begin with general questions, then ask for specific details
- **Use Document Context**: Reference specific documents when possible
- **Ask Follow-up Questions**: Build on previous responses for deeper insights
- **Verify Critical Information**: Cross-reference important information with source documents

#### Document Organization
- **Consistent Naming**: Use clear, consistent file naming conventions
- **Proper Categorization**: Organize documents in logical SharePoint folders
- **Regular Updates**: Keep documents current and remove outdated versions
- **Metadata Management**: Maintain proper document metadata for better searchability

### Security Considerations

#### Data Protection
- **Sensitive Information**: Be cautious when discussing sensitive or confidential information
- **Access Control**: Ensure proper SharePoint permissions are maintained
- **Regular Audits**: Periodically review document access and permissions
- **Data Retention**: Follow organizational data retention policies

#### Privacy Best Practices
- **Question Content**: Remember that queries may be logged for system improvement
- **Document Sharing**: Ensure documents are appropriately shared and accessible
- **User Permissions**: Maintain proper user role assignments
- **Regular Reviews**: Periodically review and update access permissions

### Performance Optimization

#### Efficient Querying
- **Specific Questions**: Ask targeted questions rather than broad, general ones
- **Reasonable Scope**: Don't ask for information that would require processing hundreds of documents
- **Patience**: Allow time for complex queries to process
- **Iterative Approach**: Break complex questions into smaller, manageable parts

#### Document Management
- **Regular Cleanup**: Remove outdated or duplicate documents
- **Size Management**: Keep document sizes reasonable for better performance
- **Format Optimization**: Use appropriate file formats for different content types
- **Batch Operations**: Perform bulk operations during off-peak hours

---

## Conclusion

The DDB RAG Application provides a powerful, intelligent way to interact with your organization's documents. By following this user manual, you'll be able to effectively use all features of the application to find information, analyze content, and gain insights from your document collection.

For additional support or advanced configuration options, please contact your system administrator or refer to the technical documentation.

---

*This user manual is for DDB RAG Application v2.6. For the most current version of this documentation, please check with your system administrator.*